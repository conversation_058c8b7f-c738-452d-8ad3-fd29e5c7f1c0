<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('pharmacy_registration_id')->nullable();
            $table->string('tenant_id')->nullable();
            $table->string('authorize_transaction_id')->nullable();
            $table->dateTime('authorize_transaction_expired_at')->nullable();
            $table->string('payment_capture_id')->nullable();
            $table->float('plan_amount');
            $table->boolean('payment_status')->default(0)->comment('0 = Failed, 1 = Success');
            $table->string('last_card_four_digits', 4)->nullable();
            $table->string('card_expiry_month', 2)->nullable();
            $table->string('card_expiry_year', 4)->nullable();
            $table->string('card_brand_type', 50)->nullable();
            $table->timestamps();

            // Foreign key constraint
            $table->foreign('pharmacy_registration_id')
                ->references('id')->on('pharmacy_registrations')
                ->onDelete('cascade');

            $table->foreign('tenant_id')->references('id')->on('tenants')->onUpdate('cascade')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_logs');
    }
};
