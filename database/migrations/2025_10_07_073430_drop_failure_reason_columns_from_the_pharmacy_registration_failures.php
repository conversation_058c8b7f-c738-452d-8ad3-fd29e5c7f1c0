<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pharmacy_registration_failures', function (Blueprint $table) {
            $table->dropColumn(['failure_reason']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pharmacy_registration_failures', function (Blueprint $table) {
            $table->string('failure_reason')->nullable()->after('payment_card_details');
        });
    }
};
