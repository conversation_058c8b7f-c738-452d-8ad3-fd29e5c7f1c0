<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_product_leads', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('product_item_id')->nullable()->constrained('product_items')->onDelete('set null');
            $table->string('name',100);
            $table->string('email',100);
            $table->string('phone_number',12);
            $table->json('product_details');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_product_leads');
    }
};
