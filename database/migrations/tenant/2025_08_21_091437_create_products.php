<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('main_category_id');
            $table->uuid('sub_category_id')->nullable();
            $table->string('slug')->unique();
            $table->string('product_type',30)->comment('single,programs');
            $table->string('product_title')->comment('product_title will be NULL for single product')->nullable();
            $table->string('image')->nullable();
            $table->boolean('is_active')->default(1);
            $table->timestamps();

            // foreign keys
            $table->foreign('main_category_id')
                  ->references('id')->on('main_categories')
                  ->cascadeOnUpdate()->cascadeOnDelete();

            $table->foreign('sub_category_id')
                  ->references('id')->on('sub_categories')
                  ->cascadeOnUpdate()->nullOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
