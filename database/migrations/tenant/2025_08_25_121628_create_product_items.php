<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_items', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('product_id')->index(); // FK to products table
            $table->string('product_name');
            $table->string('product_form')->nullable();
            $table->string('product_strength')->nullable();
            $table->string('product_qty')->nullable();
            $table->text('product_description')->nullable();
            $table->float('price');
            $table->string('xpedicare_url');
            $table->timestamps();

            // foreign key
            $table->foreign('product_id')
                  ->references('id')->on('products')
                  ->cascadeOnUpdate()->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_items');
    }
};
