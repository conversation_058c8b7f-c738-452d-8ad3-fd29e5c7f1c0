<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('app_settings', function (Blueprint $table) {
            $table->dropColumn(['terms_and_conditions_url','privacy_policy_url','support_email','support_phone_number','info_email','tiktok_url','youtube_url','linkedin_url','twitter_url','facebook_url','instagram_url']);
            $table->renameColumn('app_configs', 'lp_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('app_settings', function (Blueprint $table) {
            $table->string('terms_and_conditions_url')->nullable();
            $table->string('privacy_policy_url')->nullable();
            $table->string('instagram_url')->nullable();
            $table->string('facebook_url')->nullable();
            $table->string('twitter_url')->nullable();
            $table->string('linkedin_url')->nullable();
            $table->string('youtube_url')->nullable();
            $table->string('tiktok_url')->nullable();
            $table->string('support_email')->nullable();
            $table->string('support_phone_number')->nullable();
            $table->string('info_email')->nullable();
        });
    }
};
