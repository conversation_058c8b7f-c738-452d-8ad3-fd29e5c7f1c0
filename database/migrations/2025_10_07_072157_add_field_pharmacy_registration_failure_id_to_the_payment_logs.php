<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payment_logs', function (Blueprint $table) {
            $table->uuid('pharmacy_registration_failure_id')->nullable()->after('id');
            $table->string('failed_reason')->nullable()->after('payment_status');
            $table->foreign('pharmacy_registration_failure_id')->references('id')->on('pharmacy_registration_failures')->onDelete('set null');;
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('payment_logs', function (Blueprint $table) {
            $table->dropForeign(['pharmacy_registration_failure_id']);
            $table->dropColumn('pharmacy_registration_failure_id');
            $table->dropColumn('failed_reason');
        });
    }
};
