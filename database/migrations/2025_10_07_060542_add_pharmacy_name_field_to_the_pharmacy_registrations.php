<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('pharmacy_registrations', function (Blueprint $table) {
            $table->string('pharmacy_name',100)->after('id');
        });

        Schema::table('pharmacy_registration_failures', function (Blueprint $table) {
            $table->string('pharmacy_name',100)->after('id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pharmacy_registrations', function (Blueprint $table) {
            $table->dropColumn('pharmacy_name');
        });

        Schema::table('pharmacy_registration_failures', function (Blueprint $table) {
            $table->dropColumn('pharmacy_name');
        });
    }
};
