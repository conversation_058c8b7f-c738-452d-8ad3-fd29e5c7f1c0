<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plan_settings', function (Blueprint $table) {
            $table->json('features')->nullable()->after('description');
            $table->renameColumn('plan_name', 'plan_title');
            $table->renameColumn('description', 'summary');
            $table->float('setup_fee')->nullable()->after('image');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plan_settings', function (Blueprint $table) {
            $table->dropColumn('features');
            $table->renameColumn('plan_title', 'plan_name');
            $table->renameColumn('summary', 'description');
            $table->dropColumn('setup_fee');
        });
    }
};
