<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_settings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('plan_name');
            $table->text('description')->nullable();
            $table->string('image')->nullable();
            $table->float('monthly_price');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_settings');
    }
};
