    <?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pharmacy_registrations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('first_name',50);
            $table->string('last_name',50);
            $table->string('email',100)->unique();
            $table->string('phone_number',12)->nullable();
            $table->string('preferred_sub_domain',50)->unique();
            $table->json('address_details');
            $table->string('last_card_four_digits', 4)->nullable();
            $table->string('card_expiry_month', 2)->nullable();
            $table->string('card_expiry_year', 4)->nullable();
            $table->string('card_brand_type', 50)->nullable();
            $table->string('authorize_transaction_id')->nullable();
            $table->text('payment_vault_token')->nullable();
            $table->boolean('status')->default(0)->comment('0 = Pending, 1 = Approved, 2 = Rejected');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pharmacy_registrations');
    }
};
