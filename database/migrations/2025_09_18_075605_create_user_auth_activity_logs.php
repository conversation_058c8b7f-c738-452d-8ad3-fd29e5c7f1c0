<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_auth_activity_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id')->nullable();
            $table->boolean('status')->default(0)->comment('0 = Failed, 1 = Success');
            $table->ipAddress('ip_address')->nullable();
            $table->string('timezone', 40)->nullable();
            $table->string('location', 255)->nullable();
            $table->string('browser', 255)->nullable();
            $table->string('platform', 255)->nullable();
            $table->string('device_type', 255)->nullable();
            $table->string('user_agent', 255)->nullable();
            $table->timestamps();

            // Index
            $table->index('user_id');

            // Foreign key
            $table->foreign('user_id')
                  ->references('id')
                  ->on('users')
                  ->onDelete('cascade'); // you can change to set null if preferred
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_auth_activity_logs', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropIndex(['user_id']);
        });

        Schema::dropIfExists('user_auth_activity_logs');
    }
};
