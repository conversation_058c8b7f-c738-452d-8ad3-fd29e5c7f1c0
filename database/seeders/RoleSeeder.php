<?php

namespace Database\Seeders;

use Spatie\Permission\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;

class RoleSeeder extends Seeder
{

    public function run(): void
    {
        $roles = $this->roleList();
        foreach ($roles as  $value) {
            $role = Role::create(['name'=>$value['name'],'guard_name'=>'api']);
            $this->assignPermissionRoleWise($value,$role);
        }
    }
    public function roleList()
    {
        return [
            ['name'=>'superadmin'],
            ['name'=>'admin']
        ];
    }
    public function assignPermissionRoleWise($value,$role)
    {
        if($value['name'] == 'admin'){
            $permissions = Permission::all();
            $role->syncPermissions($permissions);
        }
    }
}
