<?php

namespace Database\Seeders\Tenant;

use App\Models\User;
use Database\Seeders\Tenant\RoleSeeder;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Artisan;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RoleSeeder::class
        ]);
        // Artisan::call('passport:client', [
        //     '--personal' => true,
        //     '--name' => 'Personal Access Client',
        //     '--provider' => 'users'
        // ]);
    }
}
