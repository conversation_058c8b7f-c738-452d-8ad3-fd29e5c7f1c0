<?php

namespace Database\Seeders;

use App\Models\State;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class StateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $states = [
            [
                "name"=> "Alabama",
                "code"=> "AL",
                "is_active" => 1
            ],
            [
                "name"=> "Alaska",
                "code"=> "AK",
                "is_active" => 1
            ],
            [
                "name"=> "Arizona",
                "code"=> "AZ",
                "is_active" => 1
            ],
            [
                "name"=> "Arkansas",
                "code"=> "AR",
                "is_active" => 1
            ],
            [
                "name"=> "California",
                "code"=> "CA",
                "is_active" => 1
            ],
            [
                "name"=> "Colorado",
                "code"=> "CO",
                "is_active" => 1
            ],
            [
                "name"=> "Connecticut",
                "code"=> "CT",
                "is_active" => 1
            ],
            [
                "name"=> "Delaware",
                "code"=> "DE",
                "is_active" => 1
            ],
            [
                "name"=> "Florida",
                "code"=> "FL",
                "is_active" => 1
            ],
            [
                "name"=> "Georgia",
                "code"=> "GA",
                "is_active" => 1
            ],
            [
                "name"=> "Hawaii",
                "code"=> "HI",
                "is_active" => 1
            ],
            [
                "name"=> "Idaho",
                "code"=> "ID",
                "is_active" => 1
            ],
            [
                "name"=> "Illinois",
                "code"=> "IL",
                "is_active" => 1
            ],
            [
                "name"=> "Indiana",
                "code"=> "IN",
                "is_active" => 1
            ],
            [
                "name"=> "Iowa",
                "code"=> "IA",
                "is_active" => 1
            ],
            [
                "name"=> "Kansas",
                "code"=> "KS",
                "is_active" => 1
            ],
            [
                "name"=> "Kentucky",
                "code"=> "KY",
                "is_active" => 1
            ],
            [
                "name"=> "Louisiana",
                "code"=> "LA",
                "is_active" => 1
            ],
            [
                "name"=> "Maine",
                "code"=> "ME",
                "is_active" => 1
            ],
            [
                "name"=> "Maryland",
                "code"=> "MD",
                "is_active" => 1
            ],
            [
                "name"=> "Massachusetts",
                "code"=> "MA",
                "is_active" => 1
            ],
            [
                "name"=> "Michigan",
                "code"=> "MI",
                "is_active" => 1
            ],
            [
                "name"=> "Minnesota",
                "code"=> "MN",
                "is_active" => 1
            ],
            [
                "name"=> "Mississippi",
                "code"=> "MS",
                "is_active" => 1
            ],
            [
                "name"=> "Missouri",
                "code"=> "MO",
                "is_active" => 1
            ],
            [
                "name"=> "Montana",
                "code"=> "MT",
                "is_active" => 1
            ],
            [
                "name"=> "Nebraska",
                "code"=> "NE",
                "is_active" => 1
            ],
            [
                "name"=> "Nevada",
                "code"=> "NV",
                "is_active" => 1
            ],
            [
                "name"=> "New Hampshire",
                "code"=> "NH",
                "is_active" => 1
            ],
            [
                "name"=> "New Jersey",
                "code"=> "NJ",
                "is_active" => 1
            ],
            [
                "name"=> "New Mexico",
                "code"=> "NM",
                "is_active" => 1
            ],
            [
                "name"=> "New York",
                "code"=> "NY",
                "is_active" => 1
            ],
            [
                "name"=> "North Carolina",
                "code"=> "NC",
                "is_active" => 1
            ],
            [
                "name"=> "North Dakota",
                "code"=> "ND",
                "is_active" => 1
            ],
            [
                "name"=> "Ohio",
                "code"=> "OH",
                "is_active" => 1
            ],
            [
                "name"=> "Oklahoma",
                "code"=> "OK",
                "is_active" => 1
            ],
            [
                "name"=> "Oregon",
                "code"=> "OR",
                "is_active" => 1
            ],
            [
                "name"=> "Pennsylvania",
                "code"=> "PA",
                "is_active" => 1
            ],
            [
                "name"=> "Rhode Island",
                "code"=> "RI",
                "is_active" => 1
            ],
            [
                "name"=> "South Carolina",
                "code"=> "SC",
                "is_active" => 1
            ],
            [
                "name"=> "South Dakota",
                "code"=> "SD",
                "is_active" => 1
            ],
            [
                "name"=> "Tennessee",
                "code"=> "TN",
                "is_active" => 1
            ],
            [
                "name"=> "Texas",
                "code"=> "TX",
                "is_active" => 1
            ],
            [
                "name"=> "Utah",
                "code"=> "UT",
                "is_active" => 1
            ],
            [
                "name"=> "Vermont",
                "code"=> "VT",
                "is_active" => 1
            ],
            [
                "name"=> "Virginia",
                "code"=> "VA",
                "is_active" => 1
            ],
            [
                "name"=> "Washington",
                "code"=> "WA",
                "is_active" => 1
            ],
            [
                "name"=> "West Virginia",
                "code"=> "WV",
                "is_active" => 1
            ],
            [
                "name"=> "Wisconsin",
                "code"=> "WI",
                "is_active" => 1
            ],
            [
                "name"=> "Wyoming",
                "code"=> "WY",
                "is_active" => 1
            ]
        ];
        foreach ($states as $key => $state) {
            State::create($state);
        }
    }
}
