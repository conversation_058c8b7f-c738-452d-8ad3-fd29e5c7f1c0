<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $user = [
            "first_name" => "RXC",
            "last_name" => "Support",
            "email"=> "<EMAIL>",
            "password"=> "Test@123"
        ];
        $user = User::create($user);
        if($user){
            $user->assignRole('superadmin');
        }
    }
}
