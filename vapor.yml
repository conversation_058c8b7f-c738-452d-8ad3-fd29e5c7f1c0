id: 73269
name: rxc-sd-multitenant
environments:
    production:
        memory: 1024
        cli-memory: 1024
        domain: "*.genrxhealth.com"
        runtime: docker
        database: rxc-sd-multitenant-db-akf65jh7
        gateway-version: 2
        build:
            - "COMPOSER_MIRROR_PATH_REPOS=1 composer install --no-dev"
            - "npm install && npm run build -- --mode production && rm -rf node_modules"
            - 'php artisan event:cache'
            - 'php artisan route:cache'
        deploy:
            - 'php artisan migrate --force'
            - 'php artisan tenants:migrate --force'
            - 'php artisan queue:restart'
