import { defineConfig } from 'vite'
import laravel from 'laravel-vite-plugin'
import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import path from 'node:path'

export default defineConfig({
  plugins: [
    laravel({
      input: ['resources/vue/central/main.ts', 'resources/vue/tenant/main.ts'],
      refresh: true,
    }),
    vue(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './resources/vue'),
      '@central': path.resolve(__dirname, './resources/vue/central'),
      '@tenant': path.resolve(__dirname, './resources/vue/tenant'),
    },
  },
  server: {
    port: 3004,
  },
})
