<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <style>
        #tenant-loader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.3s ease-out;
        }

        #tenant-loader.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .tenant-loader {
            width: 40px;
            aspect-ratio: 1;
            display: grid;
            border-radius: 50%;
            background:
                linear-gradient(0deg ,rgb(0 0 0/50%) 30%,#0000 0 70%,rgb(0 0 0/100%) 0) 50%/8% 100%,
                linear-gradient(90deg,rgb(0 0 0/25%) 30%,#0000 0 70%,rgb(0 0 0/75% ) 0) 50%/100% 8%;
            background-repeat: no-repeat;
            animation: l23 1s infinite steps(12);
        }
        .tenant-loader::before,
        .tenant-loader::after {
            content: "";
            grid-area: 1/1;
            border-radius: 50%;
            background: inherit;
            opacity: 0.915;
            transform: rotate(30deg);
        }
        .tenant-loader::after {
            opacity: 0.83;
            transform: rotate(60deg);
        }
        @keyframes l23 {
            100% {transform: rotate(1turn)}
        }
    </style>

    @vite(['resources/vue/tenant/main.ts'])
  </head>
  <body>
    <div id="tenant-loader">
        <div class="tenant-loader"></div>
    </div>
    <div id="tenant-app"></div>
  </body>
</html>
