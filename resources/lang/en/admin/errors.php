<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Authentication Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines are used during authentication for various
    | messages that we need to display to the user. You are free to modify
    | these language lines according to your application's requirements.
    |
    */

    // common error
    'record_not_found'   => "We regret to inform you that we received an invalid request. Please try again.",
    'server_error'=> 'Internal Server Error.',

    'email' => [
        'invalid_verification_code' => 'Invalid email verification code.'
    ],
    // admin login
    'invalid_credentials'   => [
        'message' => 'It appears that the login credentials you provided are invalid. Please double-check your email and password.',
    ],
    "unauthorized_access" => "You are not authorized to access this api.",
    "passport_token_error" => "Generate passport client token a new one before authentication",

    'image_required'        => 'The image field is required.',
    'invalid_base64_format' => 'The file you uploaded appears to be corrupted. Please choose another image.',
    'not_image_file'        => 'The file must be a valid image.',
    'invalid_image_data'    => 'The image data could not be decoded.',
    'image_too_large'       => 'The image size must not exceed 3 MB.',
    'should_image_file'     => 'The file must be a valid image file.',

    "category_has_sub_category" => "The deletion of this record cannot be completed at the moment as it is associated with sub categories.",
    "category_has_product" => "The deletion of this record cannot be completed at the moment as it is associated with one or more products.",
    "sub_category_already_exist" => "The sub-category already exists.",
    "category_already_exist" => "The category already exists.",
    'record_not_found'   => "We regret to inform you that we received an invalid request. Please try again.",
    "category_with_gender_already_exist" => "The category with gender already exists.",

    "product_already_exist" => "The product name has already been taken.",
    "product_title_already_exist" => "The product title has already been taken.",
    "product_item_creation_error" => "Product item creation failed.",
    "product_creation_failed" => "Product creation failed.",
    "unable_to_find_product_item_error" => "Unable to find product item.",
    "product_already_mapped" => "The product has already been mapped.",
    "id_field_required" => "The products field is required.",

    "top_product_limit" => "You can add up to 6 top products only.",
    "invalid_token" => "Invalid token.",
    "account_already_setup" => "You have already set up your account.",
    "you_are_not_unable_access_this_route" => "You are not able to access this route.",
    "link_expired" => "Your invitation link has expired. Please contact administrator.",
    "unable_to_find_user" => "Unable to find user.",
];
