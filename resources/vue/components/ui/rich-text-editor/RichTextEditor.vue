<script setup lang="ts">
import { useEditor, EditorContent } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import Underline from '@tiptap/extension-underline'
import { Button } from '@/components/ui/button'
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  List,
  ListOrdered,
  Undo,
  Redo,
  Heading1,
  Heading2,
  Heading3,
  Pilcrow,
} from 'lucide-vue-next'
import { watch } from 'vue'

interface Props {
  modelValue?: string
  placeholder?: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: 'Start typing...',
  class: '',
})

const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const editor = useEditor({
  content: props.modelValue,
  extensions: [StarterKit, Underline],
  editorProps: {
    attributes: {
      class:
        'prose prose-sm md:prose-base prose-slate focus:outline-none min-h-[120px] p-3 text-foreground',
    },
  },
  onUpdate: ({ editor }) => {
    emit('update:modelValue', editor.getHTML())
  },
})

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  (newValue) => {
    if (editor.value && editor.value.getHTML() !== newValue) {
      editor.value.commands.setContent(newValue)
    }
  },
)
</script>

<template>
  <div :class="['border rounded-md', props.class]">
    <!-- Toolbar -->
    <div v-if="editor" class="border-b p-2 flex flex-wrap gap-1">
      <!-- Text Formatting -->
      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('bold') }"
        @click="editor.chain().focus().toggleBold().run()"
      >
        <Bold class="h-4 w-4" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('italic') }"
        @click="editor.chain().focus().toggleItalic().run()"
      >
        <Italic class="h-4 w-4" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('underline') }"
        @click="editor.chain().focus().toggleUnderline().run()"
      >
        <UnderlineIcon class="h-4 w-4" />
      </Button>

      <div class="w-px h-6 bg-border mx-1"></div>

      <!-- Headings -->
      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('paragraph') }"
        @click="editor.chain().focus().setParagraph().run()"
      >
        <Pilcrow class="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('heading', { level: 1 }) }"
        @click="editor.chain().focus().toggleHeading({ level: 1 }).run()"
      >
        <Heading1 class="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('heading', { level: 2 }) }"
        @click="editor.chain().focus().toggleHeading({ level: 2 }).run()"
      >
        <Heading2 class="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('heading', { level: 3 }) }"
        @click="editor.chain().focus().toggleHeading({ level: 3 }).run()"
      >
        <Heading3 class="h-4 w-4" />
      </Button>

      <div class="w-px h-6 bg-border mx-1"></div>

      <!-- Lists -->
      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('bulletList') }"
        @click="editor.chain().focus().toggleBulletList().run()"
      >
        <List class="h-4 w-4" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        type="button"
        :class="{ 'bg-muted': editor.isActive('orderedList') }"
        @click="editor.chain().focus().toggleOrderedList().run()"
      >
        <ListOrdered class="h-4 w-4" />
      </Button>

      <div class="w-px h-6 bg-border mx-1"></div>

      <!-- Undo/Redo -->
      <Button
        variant="ghost"
        size="sm"
        type="button"
        :disabled="!editor.can().undo()"
        @click="editor.chain().focus().undo().run()"
      >
        <Undo class="h-4 w-4" />
      </Button>

      <Button
        variant="ghost"
        size="sm"
        type="button"
        :disabled="!editor.can().redo()"
        @click="editor.chain().focus().redo().run()"
      >
        <Redo class="h-4 w-4" />
      </Button>
    </div>

    <!-- Editor Content -->
    <EditorContent :editor="editor" :placeholder="placeholder" class="min-h-[120px]" />
  </div>
</template>

<style scoped>
:deep(.prose h1) {
  color: hsl(var(--foreground));
}

:deep(.prose h2) {
  color: hsl(var(--foreground));
}

:deep(.prose h3) {
  color: hsl(var(--foreground));
}
:deep(.prose strong) {
  color: hsl(var(--foreground));
}
</style>
