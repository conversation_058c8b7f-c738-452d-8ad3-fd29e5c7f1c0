<script setup lang="ts">
import { onMounted, watch } from 'vue'
import { storeToRefs } from 'pinia'
import { useRouter } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

import { Search, XIcon, Eye } from 'lucide-vue-next'
import { usePharmacyRequestStore } from '@central/stores'
import type { AcceptableValue } from 'reka-ui'
import { useDebounce } from '@vueuse/core'
import { formatPhone } from '@/lib'
import { Badge } from '@/components/ui/badge'

const pharmacyRequestStore = usePharmacyRequestStore()
const { pharmacies, isLoadingList, pagination, searchQuery, sortBy } =
  storeToRefs(pharmacyRequestStore)
const { fetchPharmacyList } = pharmacyRequestStore

const router = useRouter()
const debouncedSearchQuery = useDebounce(searchQuery, 300)

const handleSearch = async () => {
  pagination.value.currentPage = 1
  await fetchPharmacyList()
}

const handlePageChange = async (page: number) => {
  pagination.value.currentPage = page
  await fetchPharmacyList()
}

const handlePerPageChange = async (perPage: AcceptableValue) => {
  pagination.value.perPage = Number(perPage)
  pagination.value.currentPage = 1
  await fetchPharmacyList()
}

const handleSort = async (column: string) => {
  if (sortBy.value.column === column) {
    sortBy.value.direction = sortBy.value.direction === 'asc' ? 'desc' : 'asc'
  } else {
    sortBy.value.column = column
    sortBy.value.direction = 'desc'
  }
  await fetchPharmacyList()
}

// async function resetFilters() {
//   searchQuery.value = ''
//   statusFilter.value = 'all'
//   sortBy.value = { column: '', direction: 'desc' }
//   pagination.value.currentPage = 1
//   pagination.value.perPage = 10
//   await fetchPharmacyList()
// }

function navigateToDetails(id: string) {
  router.push({ name: 'pharmacy-requests-details', params: { id } })
}

watch(
  () => debouncedSearchQuery.value,
  () => {
    handleSearch()
  },
)

onMounted(async () => {
  await fetchPharmacyList()
})
</script>

<template>
  <div class="space-y-6">
    <div class="flex flex-col gap-4 sm:flex-row sm:gap-0 sm:items-center sm:justify-between">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">Pharmacy Onboarding Requests</h1>
        <p class="text-muted-foreground text-sm">Manage pharmacy onboarding requests</p>
      </div>
    </div>

    <Card>
      <CardHeader class="pb-3">
        <div
          class="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-x-4 sm:space-y-0"
        >
          <div class="relative w-full sm:max-w-sm">
            <Search class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              v-model="searchQuery"
              placeholder="Search by name, email..."
              class="pl-8"
              @keyup.enter="handleSearch"
            />
            <XIcon
              v-if="searchQuery"
              class="absolute right-2.5 top-2.5 h-4 w-4 cursor-pointer text-muted-foreground"
              @click="searchQuery = ''"
            />
          </div>
          <div class="flex items-center space-x-2">
            <!-- <Select v-model="statusFilter" @update:model-value="handleSearch">
              <SelectTrigger class="w-[180px]">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="0">Pending</SelectItem>
                <SelectItem value="1">Approved</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" @click="resetFilters"> Reset </Button> -->
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>#</TableHead>
                <TableHead class="min-w-[150px]">Pharmacy Name</TableHead>
                <TableHead class="min-w-[150px]">Contact Person</TableHead>
                <TableHead class="min-w-[150px]">Status</TableHead>
                <TableHead
                  class="cursor-pointer hover:bg-muted/50 min-w-[150px]"
                  @click="handleSort('created_at')"
                >
                  <div class="flex items-center">
                    Created At
                    <span v-if="sortBy.column === 'created_at'" class="ml-1">
                      {{ sortBy.direction === 'asc' ? '↑' : '↓' }}
                    </span>
                  </div>
                </TableHead>
                <TableHead class="min-w-[120px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <!-- Loading -->
              <template v-if="isLoadingList">
                <TableRow>
                  <TableCell colspan="6" class="h-24 text-center"> Loading... </TableCell>
                </TableRow>
              </template>

              <!-- No results -->
              <template v-else-if="pharmacies.length === 0">
                <TableRow>
                  <TableCell colspan="6" class="h-24 text-center"> No results found. </TableCell>
                </TableRow>
              </template>

              <!-- Data -->
              <template v-else>
                <TableRow v-for="(pharmacy, index) in pharmacies" :key="pharmacy.id">
                  <TableCell class="font-medium">
                    {{ index + 1 + (pagination.currentPage - 1) * pagination.perPage }}
                  </TableCell>
                  <TableCell>
                    <div class="cursor-pointer" @click="navigateToDetails(pharmacy.id)">
                      {{ pharmacy.pharmacy_name }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>{{ pharmacy.first_name }} {{ pharmacy.last_name }}</div>
                    <div v-if="pharmacy.email" class="text-xs text-muted-foreground">
                      {{ pharmacy.email }}
                    </div>
                    <div v-if="pharmacy.phone_number" class="text-xs text-muted-foreground">
                      {{ formatPhone(pharmacy.phone_number) }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge :variant="pharmacy.status ? 'default' : 'secondary'">
                      {{ pharmacy.status ? 'Approved' : 'Pending' }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div>{{ pharmacy.created_date }}</div>
                    <div class="text-xs text-muted-foreground">
                      {{ pharmacy.created_time }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        @click="navigateToDetails(pharmacy.id)"
                        title="View Details"
                      >
                        <Eye class="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              </template>
            </TableBody>
          </Table>
        </div>

        <!-- Pagination -->
        <div
          class="flex flex-col gap-4 sm:flex-row sm:gap-0 items-center justify-between px-2 mt-4"
        >
          <div class="flex items-center space-x-2">
            <p class="text-sm text-muted-foreground">
              Page {{ pagination.currentPage }} of {{ pagination.totalPages }}
            </p>
            <Select :model-value="pagination.perPage" @update:model-value="handlePerPageChange">
              <SelectTrigger class="h-8">
                <SelectValue :placeholder="pagination.perPage.toString()" />
              </SelectTrigger>
              <SelectContent side="top">
                <SelectItem
                  v-for="pageSize in [10, 25, 50, 100]"
                  :key="pageSize"
                  :value="pageSize"
                  class="cursor-pointer"
                >
                  {{ pageSize }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div v-if="pharmacies.length" class="text-sm text-muted-foreground">
            Showing {{ pharmacies.length }} of {{ pagination.totalRecords }} requests
          </div>
          <div class="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage === 1"
              @click="handlePageChange(pagination.currentPage - 1)"
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              :disabled="pagination.currentPage >= pagination.totalPages"
              @click="handlePageChange(pagination.currentPage + 1)"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
