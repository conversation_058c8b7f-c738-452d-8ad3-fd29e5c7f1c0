<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { ArrowLeft, Loader2, Eye } from 'lucide-vue-next'
import { usePharmacyRequestStore } from '@central/stores'
import { storeToRefs } from 'pinia'
import PaymentLogDetailsModal from '@central/components/PaymentLogDetailsModal.vue'

const pharmacyRequestStore = usePharmacyRequestStore()
const { pharmacyDetail, isLoadingDetail, error } = storeToRefs(pharmacyRequestStore)
const { fetchPharmacyDetail } = pharmacyRequestStore

const router = useRouter()
const route = useRoute()
const pharmacyId = computed(() => route.params.id as string)

const isPaymentLogModalOpen = ref(false)
const selectedPaymentLogId = ref<string>('')

function getStatusBadge(status: 0 | 1) {
  switch (status) {
    case 0:
      return { variant: 'secondary' as const, text: 'Pending' }
    case 1:
      return { variant: 'default' as const, text: 'Approved' }
    default:
      return { variant: 'destructive' as const, text: 'Unknown' }
  }
}

function getPaymentStatusBadge(status: 0 | 1) {
  switch (status) {
    case 0:
      return { variant: 'destructive' as const, text: 'Failed' }
    case 1:
      return { variant: 'default' as const, text: 'Success' }
    default:
      return { variant: 'secondary' as const, text: 'Unknown' }
  }
}

function openPaymentLogModal(paymentLogId: string) {
  selectedPaymentLogId.value = paymentLogId
  isPaymentLogModalOpen.value = true
}

async function loadPharmacyDetails() {
  if (pharmacyId.value) {
    await fetchPharmacyDetail(pharmacyId.value)
  }
}

onMounted(() => {
  loadPharmacyDetails()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3 mb-6">
      <Button variant="ghost" size="sm" @click="router.back()">
        <ArrowLeft class="h-4 w-4" />
        <span>Back</span>
      </Button>
      <h1 class="text-2xl font-bold">Pharmacy Details</h1>
    </div>

    <div v-if="isLoadingDetail" class="flex items-center justify-center py-8">
      <Loader2 class="h-8 w-8 animate-spin" />
      <span class="ml-2">Loading pharmacy details...</span>
    </div>

    <div v-else-if="error" class="text-center py-8">
      <p class="text-destructive">{{ error }}</p>
      <Button @click="loadPharmacyDetails" class="mt-4"> Try Again </Button>
    </div>

    <div v-else-if="pharmacyDetail" class="space-y-6">
      <!-- Basic Information -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Basic Information</CardTitle>
            <Badge :variant="getStatusBadge(pharmacyDetail.status).variant">
              {{ getStatusBadge(pharmacyDetail.status).text }}
            </Badge>
          </div>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Pharmacy Name</label>
              <p class="text-sm">{{ pharmacyDetail.pharmacy_name }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Preferred Subdomain</label>
              <p class="text-sm">{{ pharmacyDetail.preferred_sub_domain }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Contact Person</label>
              <p class="text-sm">{{ pharmacyDetail.first_name }} {{ pharmacyDetail.last_name }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Email</label>
              <p class="text-sm">{{ pharmacyDetail.email }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Phone Number</label>
              <p class="text-sm">{{ pharmacyDetail.phone_number }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Address Information -->
      <Card>
        <CardHeader>
          <CardTitle>Address Information</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Address Line 1</label>
              <p class="text-sm">{{ pharmacyDetail.address_details?.address_line_1 || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Address Line 2</label>
              <p class="text-sm">{{ pharmacyDetail.address_details?.address_line_2 || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">City</label>
              <p class="text-sm">{{ pharmacyDetail.address_details?.city || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">State</label>
              <p class="text-sm">{{ pharmacyDetail.address_details?.state || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">ZIP Code</label>
              <p class="text-sm">{{ pharmacyDetail.address_details?.zipcode || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Country</label>
              <p class="text-sm">{{ pharmacyDetail.address_details?.country || 'N/A' }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Payment Information -->
      <Card>
        <CardHeader>
          <CardTitle>Payment Information</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Card Last 4 Digits</label>
              <p class="text-sm">
                **** **** **** {{ pharmacyDetail.last_card_four_digits || 'N/A' }}
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Card Brand</label>
              <p class="text-sm">{{ pharmacyDetail.card_brand_type || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Card Expiry</label>
              <p class="text-sm">
                {{
                  pharmacyDetail.card_expiry_month && pharmacyDetail.card_expiry_year
                    ? `${pharmacyDetail.card_expiry_month}/${pharmacyDetail.card_expiry_year}`
                    : 'N/A'
                }}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Payment Logs -->
      <Card>
        <CardHeader>
          <CardTitle>Payment Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Amount</TableHead>
                  <TableHead>Transaction Type</TableHead>
                  <TableHead>Payment Status</TableHead>
                  <TableHead>Card Info</TableHead>
                  <TableHead>Transaction ID</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead class="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow
                  v-if="!pharmacyDetail.payment_logs || pharmacyDetail.payment_logs.length === 0"
                >
                  <TableCell colspan="7" class="text-center py-8 text-muted-foreground">
                    No payment logs found
                  </TableCell>
                </TableRow>
                <TableRow v-else v-for="log in pharmacyDetail.payment_logs" :key="log.id">
                  <TableCell class="font-medium">${{ log.plan_amount.toFixed(2) }}</TableCell>
                  <TableCell>{{ log.transaction_type }}</TableCell>
                  <TableCell>
                    <Badge :variant="getPaymentStatusBadge(log.payment_status).variant">
                      {{ getPaymentStatusBadge(log.payment_status).text }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div class="text-sm">
                      <div>**** {{ log.last_card_four_digits }}</div>
                      <div class="text-muted-foreground">{{ log.card_brand_type }}</div>
                    </div>
                  </TableCell>
                  <TableCell class="font-mono text-sm">{{ log.transaction_id }}</TableCell>
                  <TableCell>
                    <div class="text-sm">
                      <div>{{ log.created_date }}</div>
                      <div class="text-muted-foreground">{{ log.created_time }}</div>
                    </div>
                  </TableCell>
                  <TableCell class="text-right">
                    <Button variant="ghost" size="sm" @click="openPaymentLogModal(log.id)">
                      <Eye class="h-4 w-4 mr-1" />
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Payment Log Details Modal -->
    <PaymentLogDetailsModal
      :open="isPaymentLogModalOpen"
      :payment-log-id="selectedPaymentLogId"
      @update:open="isPaymentLogModalOpen = $event"
    />
  </div>
</template>
