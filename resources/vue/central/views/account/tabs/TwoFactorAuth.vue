<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { toast } from 'vue-sonner'
import { Shield, Smartphone, Key, Eye, EyeOff } from 'lucide-vue-next'
import { apiClient } from '@central/composables'
import type { ApiResponse } from '@/types/api'
import { processErrors } from '@/lib'
import QrcodeVue from 'qrcode.vue'

const twoFactorEnabled = ref(false)
const qrCodeUrl = ref('')
const secret = ref('')
const isLoading2FAStatus = ref(false)
const isEnabling2FA = ref(false)
const isVerifying2FA = ref(false)
const isDisabling2FA = ref(false)
const showQRCode = ref(false)
const showPasswordForm = ref(false)
const showPassword = ref(false)
const formError = ref('')
const passwordInputValue = ref('')
const passwordError = ref<string | null>(null)

const verificationSchema = toTypedSchema(
  z.object({
    code: z.string().min(6, 'Code must be 6 digits').max(6, 'Code must be 6 digits'),
  }),
)

const verificationForm = useForm({
  validationSchema: verificationSchema,
  initialValues: { code: '' },
})

const get2FAStatus = async () => {
  try {
    isLoading2FAStatus.value = true
    const response = await apiClient.get<ApiResponse & { is_2fa_enabled: boolean }>('/2fa/status')

    if (response.data.status === 200) {
      twoFactorEnabled.value = response.data.is_2fa_enabled
    } else {
      toast.error(response.data.message || 'Failed to get 2FA status')
    }
  } catch (error: any) {
    console.error('Error getting 2FA status:', error)
    toast.error(processErrors(error, 'Failed to get 2FA status'))
  } finally {
    isLoading2FAStatus.value = false
  }
}

const handleEnable2FA = () => {
  showPasswordForm.value = true
  formError.value = ''
}

const handleDisable2FA = () => {
  showPasswordForm.value = true
  formError.value = ''
}

const handlePasswordSubmit = async () => {
  if (!passwordInputValue.value) {
    passwordError.value = 'Password is required'
    return
  }

  passwordError.value = null

  if (twoFactorEnabled.value) {
    await disable2FA(passwordInputValue.value)
  } else {
    await enable2FA(passwordInputValue.value)
  }
}

const enable2FA = async (password: string) => {
  try {
    isEnabling2FA.value = true
    formError.value = ''

    const response = await apiClient.post<ApiResponse & { qrCodeUrl: string; secret: string }>(
      '/2fa/enable',
      {
        password,
      },
    )

    if (response.data.status === 200) {
      qrCodeUrl.value = response.data.qrCodeUrl
      secret.value = response.data.secret
      showQRCode.value = true
      showPasswordForm.value = false
      toast.success('Scan the QR code with your authenticator app')
    } else {
      formError.value = processErrors(response.data) || 'Failed to enable 2FA'
    }
  } catch (error: any) {
    console.error('Error enabling 2FA:', error)
    formError.value = processErrors(error, 'Failed to enable 2FA')
  } finally {
    isEnabling2FA.value = false
    passwordInputValue.value = ''
  }
}

const disable2FA = async (password: string) => {
  try {
    isDisabling2FA.value = true
    formError.value = ''

    const response = await apiClient.post<ApiResponse>('/2fa/disable', {
      password,
    })

    if (response.data.status === 200) {
      twoFactorEnabled.value = false
      showPasswordForm.value = false
      showQRCode.value = false
      qrCodeUrl.value = ''
      secret.value = ''
      toast.success(response.data.message || 'Two-factor authentication disabled successfully')
    } else {
      formError.value = processErrors(response.data) || 'Failed to disable 2FA'
    }
  } catch (error: any) {
    console.error('Error disabling 2FA:', error)
    formError.value = processErrors(error, 'Failed to disable 2FA')
  } finally {
    isDisabling2FA.value = false
    passwordInputValue.value = ''
  }
}

const handleVerificationSubmit = verificationForm.handleSubmit(async (values) => {
  await verifyCode(values.code)
})

const verifyCode = async (code: string) => {
  try {
    isVerifying2FA.value = true

    const response = await apiClient.post<ApiResponse>('/2fa/verify', {
      code,
    })

    if (response.data.status === 200) {
      twoFactorEnabled.value = true
      showQRCode.value = false
      verificationForm.resetForm()
      toast.success(response.data.message || 'Two-factor authentication enabled successfully')
    } else {
      toast.error(response.data.message || 'Invalid verification code')
    }
  } catch (error: any) {
    console.error('Error verifying 2FA code:', error)
    toast.error(processErrors(error, 'Invalid verification code'))
  } finally {
    isVerifying2FA.value = false
  }
}

onMounted(async () => {
  await get2FAStatus()
})
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="flex items-center gap-2">
        <Shield class="h-5 w-5" />
        Two Factor Authentication
      </CardTitle>
      <CardDescription> Add an extra layer of security to your account. </CardDescription>
    </CardHeader>
    <CardContent class="space-y-6">
      <!-- Loading State -->
      <div v-if="isLoading2FAStatus" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>

      <!-- 2FA Status and Toggle -->
      <div v-else class="space-y-6">
        <div class="flex items-center justify-between p-4 border rounded-lg">
          <div class="space-y-0.5">
            <label class="text-base font-medium">Two-Factor Authentication</label>
            <p class="text-sm text-muted-foreground">
              Use an authenticator app to generate verification codes.
            </p>
          </div>
          <div class="flex items-center gap-3">
            <Badge
              v-if="twoFactorEnabled"
              variant="outline"
              class="border-green-600 text-green-700 bg-green-50"
            >
              Enabled
            </Badge>
            <Badge v-else variant="outline"> Disabled </Badge>
            <Button
              v-if="!twoFactorEnabled && !showPasswordForm && !showQRCode"
              size="sm"
              @click="handleEnable2FA"
            >
              Enable 2FA
            </Button>
            <Button
              v-if="twoFactorEnabled && !showPasswordForm"
              variant="outline"
              size="sm"
              @click="handleDisable2FA"
            >
              Disable 2FA
            </Button>
          </div>
        </div>

        <!-- Password Form -->
        <div v-if="showPasswordForm" class="space-y-4 border rounded-lg p-4">
          <div>
            <h4 class="font-medium text-lg">
              {{ twoFactorEnabled ? 'Disable' : 'Enable' }} Two-Factor Authentication
            </h4>
            <p class="text-sm text-muted-foreground">
              Please confirm your password to {{ twoFactorEnabled ? 'disable' : 'enable' }} 2FA.
            </p>
          </div>

          <div class="space-y-4">
            <div class="space-y-2">
              <label class="text-sm font-medium leading-none"> Password </label>
              <div class="relative">
                <Input
                  v-model="passwordInputValue"
                  placeholder="Enter your password"
                  :type="showPassword ? 'text' : 'password'"
                  @update:model-value="passwordError = null"
                  @keyup.enter="handlePasswordSubmit()"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="icon"
                  class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  @click="showPassword = !showPassword"
                  tabindex="-1"
                >
                  <Eye v-if="showPassword" class="h-4 w-4" />
                  <EyeOff v-else class="h-4 w-4" />
                  <span class="sr-only">{{ showPassword ? 'Hide' : 'Show' }} password</span>
                </Button>
              </div>
              <p v-if="passwordError" class="text-sm font-medium text-destructive">
                {{ passwordError }}
              </p>
            </div>

            <Alert v-if="formError" variant="destructive">
              <AlertDescription>{{ formError }}</AlertDescription>
            </Alert>

            <div class="flex gap-2">
              <Button
                type="button"
                :disabled="isEnabling2FA || isDisabling2FA"
                @click="handlePasswordSubmit()"
              >
                Confirm Password
              </Button>
              <Button
                type="button"
                variant="outline"
                @click="showPasswordForm = false"
                :disabled="isEnabling2FA || isDisabling2FA"
              >
                Cancel
              </Button>
            </div>
          </div>
        </div>

        <!-- QR Code Setup -->
        <div v-if="showQRCode && !twoFactorEnabled" class="space-y-4 border rounded-lg p-4">
          <Alert>
            <Smartphone class="h-4 w-4" />
            <AlertDescription>
              Scan this QR code with your authenticator app (e.g., Google Authenticator, Authy).
            </AlertDescription>
          </Alert>

          <div class="flex justify-center">
            <div class="bg-white p-2 rounded-lg border inline-block">
              <qrcode-vue
                v-if="qrCodeUrl"
                :value="qrCodeUrl"
                :size="200"
                level="H"
                class="w-48 h-48"
              ></qrcode-vue>
              <div v-else class="w-48 h-48 bg-gray-100 flex items-center justify-center rounded">
                <div class="text-center text-sm text-gray-500">
                  <Key class="h-8 w-8 mx-auto mb-2" />
                  Generating QR Code...
                </div>
              </div>
            </div>
          </div>

          <p class="text-sm text-muted-foreground text-center">
            Or enter this code manually:
            <span class="text-foreground font-mono font-medium">{{ secret }}</span>
          </p>

          <form @submit="handleVerificationSubmit">
            <div class="space-y-4">
              <FormField v-slot="{ componentField }" name="code">
                <FormItem>
                  <FormLabel>Enter verification code from your app</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="000000"
                      v-bind="componentField"
                      maxlength="6"
                      class="text-center text-lg tracking-widest"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <Button type="submit" :disabled="isVerifying2FA" class="w-full">
                {{ isVerifying2FA ? 'Verifying...' : 'Verify & Enable 2FA' }}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
