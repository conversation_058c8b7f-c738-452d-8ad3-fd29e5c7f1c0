<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useFieldArray, useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { toast } from 'vue-sonner'
import { planSchema, usePlanStore } from '@central/stores'
import type { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Loader2, Edit, Image, FileUp, Settings, Plus, Trash2 } from 'lucide-vue-next'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { formatCurrency, isEmptyObject, scrollToTop } from '@/lib'

type PlanPayload = z.infer<typeof planSchema>

const planStore = usePlanStore()
const { planDetails, isLoadingDetails, isUpdating, error, fieldErrors } = storeToRefs(planStore)
const { fetchPlan, updatePlan } = planStore

const isEditing = ref(false)
const fileInputRef = ref<HTMLInputElement | null>(null)
const newImageSelected = ref(false)

const formSchema = toTypedSchema(planSchema)
const form = useForm<PlanPayload>({
  validationSchema: formSchema,
  initialValues: {
    features: [],
    is_active: 1,
  },
})

const {
  fields: featureFields,
  push: addFeature,
  remove: removeFeature,
} = useFieldArray<string | null>('features')

async function startEditing() {
  if (planDetails.value) {
    form.setValues({
      plan_title: planDetails.value.plan_title,
      summary: planDetails.value.summary || '',
      monthly_price: planDetails.value.monthly_price,
      image: planDetails.value.image || '',
      setup_fee: planDetails.value.setup_fee,
      features: planDetails.value.features || [],
      is_active: planDetails.value.is_active,
    })
  } else {
    form.resetForm()
  }
  isEditing.value = true
}

function cancelEditing() {
  isEditing.value = false
  form.resetForm()
  fieldErrors.value = {}
  error.value = null
}

function triggerImagePicker() {
  fileInputRef.value?.click()
}

function handleImageChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return

  if (file.size > 3 * 1024 * 1024) {
    toast.error('Please upload an image smaller than 3MB.')
    if (fileInputRef.value) fileInputRef.value.value = ''
    return
  }

  const reader = new FileReader()
  reader.onload = () => {
    const base64 = reader.result as string
    form.setFieldValue('image', base64)
    form.validateField('image')
    newImageSelected.value = true
    if (fileInputRef.value) fileInputRef.value.value = ''
  }
  reader.onerror = () => {
    toast.error('Failed to read image file')
    if (fileInputRef.value) fileInputRef.value.value = ''
  }
  reader.readAsDataURL(file)
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isUpdating.value) return

  if (!newImageSelected.value) {
    delete values.image
  }

  const result = await updatePlan(values)

  if (result) {
    await fetchPlan()
    isEditing.value = false
    newImageSelected.value = false
    scrollToTop()
  } else if (!isEmptyObject(fieldErrors.value)) {
    Object.entries(fieldErrors.value).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof PlanPayload, messages[0])
      }
    })
  } else if (error.value) {
    toast.error(error.value)
  } else {
    toast.error('Failed to update plan. Please try again.')
  }
})

onMounted(async () => {
  await fetchPlan()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center justify-between mb-6">
      <h1 class="text-2xl font-bold">Subscription Plan</h1>
    </div>

    <!-- Loading State -->
    <div v-if="isLoadingDetails" class="flex items-center justify-center py-12">
      <Loader2 class="h-8 w-8 animate-spin" />
      <span class="ml-2">Loading plan details...</span>
    </div>

    <!-- Form View -->
    <div v-else-if="isEditing">
      <Card>
        <CardHeader>
          <CardTitle>{{ planDetails ? 'Edit Plan' : 'Configure Plan' }}</CardTitle>
          <CardDescription>
            Define the details of your subscription plan for pharmacies.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form class="space-y-8" @submit.prevent="onSubmit">
            <Alert v-if="error" variant="destructive">
              <AlertDescription>{{ error }}</AlertDescription>
            </Alert>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField name="plan_title" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Plan Title *</FormLabel>
                  <FormControl>
                    <Input v-bind="componentField" :aria-invalid="!!errorMessage" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="monthly_price" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Monthly Price *</FormLabel>
                  <FormControl>
                    <div class="relative w-full items-center">
                      <Input
                        type="number"
                        v-bind="componentField"
                        class="pl-8"
                        :aria-invalid="!!errorMessage"
                      />
                      <span
                        class="absolute start-1 inset-y-0 flex items-center justify-center px-2"
                      >
                        <span class="text-muted-foreground">$</span>
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="setup_fee" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Setup Fee (optional)</FormLabel>
                  <FormControl>
                    <div class="relative w-full items-center">
                      <Input
                        type="number"
                        v-bind="componentField"
                        class="pl-8"
                        :aria-invalid="!!errorMessage"
                      />
                      <span
                        class="absolute start-1 inset-y-0 flex items-center justify-center px-2"
                      >
                        <span class="text-muted-foreground">$</span>
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>

            <FormField name="image">
              <FormItem>
                <FormLabel>Plan Image (optional)</FormLabel>
                <div class="flex items-center gap-4">
                  <img
                    v-if="form.values.image"
                    :src="form.values.image"
                    alt="Plan image preview"
                    class="h-24 w-24 rounded-lg object-cover border"
                  />
                  <div
                    v-else
                    class="h-24 w-24 rounded-lg border flex items-center justify-center bg-muted"
                  >
                    <Image class="h-10 w-10 text-muted-foreground" />
                  </div>
                  <div class="flex flex-col gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      :disabled="isUpdating"
                      @click="triggerImagePicker"
                    >
                      <FileUp class="h-4 w-4 mr-2" />
                      Upload Image
                    </Button>
                    <p class="text-xs text-muted-foreground">
                      Recommended size: 400x300px. Max 3MB.
                    </p>
                  </div>
                  <input
                    ref="fileInputRef"
                    type="file"
                    accept="image/png, image/jpeg, image/jpg"
                    class="hidden"
                    @change="handleImageChange"
                  />
                </div>
                <FormMessage />
              </FormItem>
            </FormField>

            <FormField name="summary" v-slot="{ componentField, errorMessage }">
              <FormItem>
                <FormLabel>Summary (optional)</FormLabel>
                <FormControl>
                  <Textarea
                    v-bind="componentField"
                    placeholder="A brief summary of the plan."
                    class="min-h-[120px]"
                    maxlength="2000"
                    :aria-invalid="!!errorMessage"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>

            <div>
              <p class="text-sm font-medium">Features (optional)</p>
              <div class="space-y-2 mt-2">
                <div
                  v-for="(field, index) in featureFields"
                  :key="field.key"
                  class="flex items-center gap-2"
                >
                  <FormField :name="`features[${index}]`" v-slot="{ componentField, errorMessage }">
                    <FormItem class="flex-grow">
                      <FormControl>
                        <Input
                          v-bind="componentField"
                          :placeholder="`Feature ${index + 1}`"
                          :aria-invalid="!!errorMessage"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  </FormField>
                  <Button type="button" variant="ghost" size="icon" @click="removeFeature(index)">
                    <Trash2 class="h-4 w-4" />
                  </Button>
                </div>
                <Button type="button" variant="outline" size="sm" @click="addFeature(null)">
                  <Plus class="h-4 w-4 mr-2" />
                  Add Feature
                </Button>
              </div>
            </div>

            <!-- Plan Status Toggle -->
            <FormField name="is_active" v-slot="{ field, errorMessage }">
              <FormItem>
                <div class="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div class="space-y-0.5">
                    <FormLabel class="text-base">Plan Status</FormLabel>
                    <div class="text-sm text-muted-foreground">
                      Enable or disable this subscription plan for new pharmacies
                    </div>
                  </div>
                  <FormControl>
                    <Switch
                      v-model="field.value"
                      :aria-invalid="!!errorMessage"
                      @update:model-value="field.onChange($event ? 1 : 0)"
                    />
                  </FormControl>
                </div>
                <FormMessage />
              </FormItem>
            </FormField>

            <div class="flex justify-end gap-3 pt-4 border-t">
              <Button type="button" variant="outline" :disabled="isUpdating" @click="cancelEditing">
                Cancel
              </Button>
              <Button type="submit" :disabled="isUpdating">
                <Loader2 v-if="isUpdating" class="mr-2 h-4 w-4 animate-spin" />
                {{ isUpdating ? 'Saving...' : 'Save Changes' }}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>

    <!-- Display View -->
    <div v-else-if="planDetails">
      <Card>
        <CardHeader class="flex flex-row items-start justify-between">
          <div class="flex items-center gap-3">
            <div>
              <CardTitle>Current Plan</CardTitle>
              <CardDescription>
                {{
                  planDetails.is_active === 1
                    ? 'This is the active subscription plan for new pharmacies.'
                    : 'This subscription plan is currently inactive.'
                }}
              </CardDescription>
            </div>
          </div>
          <Button variant="outline" size="sm" @click="startEditing">
            <Edit class="h-4 w-4 mr-2" />
            Edit Plan
          </Button>
        </CardHeader>
        <CardContent class="space-y-6">
          <div class="flex flex-col md:flex-row gap-6">
            <img
              v-if="planDetails.image"
              :src="planDetails.image"
              alt="Plan image"
              class="w-full md:w-1/3 h-auto rounded-lg object-cover border"
            />
            <div class="flex-1 space-y-4">
              <h2 class="text-2xl font-bold inline-flex items-center flex-wrap gap-4">
                <span>{{ planDetails.plan_title }}</span>
                <Badge :variant="planDetails.is_active === 1 ? 'default' : 'secondary'">
                  {{ planDetails.is_active === 1 ? 'Active' : 'Inactive' }}
                </Badge>
              </h2>
              <div class="flex flex-col sm:flex-row items-baseline gap-4">
                <p class="text-3xl font-semibold text-primary">
                  {{ formatCurrency(planDetails.monthly_price) }}
                  <span class="text-base font-normal text-muted-foreground">/ month</span>
                </p>
                <p v-if="planDetails.setup_fee > 0" class="text-xl font-semibold text-primary">
                  + {{ formatCurrency(planDetails.setup_fee) }}
                  <span class="text-base font-normal text-muted-foreground">setup fee</span>
                </p>
              </div>
              <p v-if="planDetails.summary" class="text-muted-foreground">
                {{ planDetails.summary }}
              </p>
              <p v-else class="text-muted-foreground italic">No summary provided.</p>

              <div
                v-if="planDetails.features && planDetails.features.length > 0"
                class="space-y-2 pt-4"
              >
                <h4 class="font-semibold">Features:</h4>
                <ul class="list-disc text-muted-foreground space-y-1 ps-6">
                  <li v-for="(feature, index) in planDetails.features" :key="index">
                    {{ feature }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Empty State -->
    <div v-else>
      <Card class="border-dashed">
        <CardContent class="pt-6">
          <div class="flex flex-col items-center justify-center text-center py-12">
            <div class="p-4 rounded-full bg-muted mb-4">
              <Settings class="h-10 w-10 text-muted-foreground" />
            </div>
            <h3 class="text-xl font-semibold mb-2">No Subscription Plan Configured</h3>
            <p class="text-muted-foreground mb-6 max-w-md">
              Create and define your subscription plan to allow new pharmacies to sign up and access
              your service.
            </p>
            <Button @click="startEditing">
              <Plus class="h-4 w-4 mr-2" />
              Configure Plan
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
