<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { ArrowLeft, Loader2 } from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { usePharmacyStore } from '@central/stores/pharmacyStore'
import type { PharmacyPayload } from '@central/types/pharmacy'
import { storeToRefs } from 'pinia'
import { isEmptyObject, scrollToTop } from '@/lib'
import { vMaska } from 'maska/vue'

const pharmacyStore = usePharmacyStore()
const { error: storeError, isAdding, fieldErrors } = storeToRefs(pharmacyStore)
const { addPharmacy } = pharmacyStore

const router = useRouter()
const formError = ref('')

const formSchema = toTypedSchema(
  z.object({
    pharmacy_name: z.string().min(1, 'Pharmacy name is required'),
    sub_domain: z
      .string()
      .min(3, 'Subdomain must be at least 3 characters')
      .regex(
        /^[a-z0-9]+(?:-[a-z0-9]+)*$/,
        'Subdomain can only contain lowercase letters, numbers, and hyphens',
      ),
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    email: z.string().email('Invalid email address'),
    phone_number: z.string().min(1, 'Phone number is required'),
  }),
)

const form = useForm<PharmacyPayload>({
  validationSchema: formSchema,
  initialValues: {
    pharmacy_name: '',
    sub_domain: '',
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
  },
})

const isSubdomainManuallyEdited = ref(false)

function generateSubdomain(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '') // Remove non-alphanumeric characters except spaces
    .trim()
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

function handleSubdomainInput(event: Event) {
  const input = event.target as HTMLInputElement
  const value = input.value

  const filteredValue = value
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, '') // Only allow lowercase letters, numbers, and hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
  // .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens

  form.setFieldValue('sub_domain', filteredValue)

  if (value) {
    // isSubdomainManuallyEdited.value = true
  }
}

watch(
  () => form.values.pharmacy_name,
  (newName) => {
    if (!isSubdomainManuallyEdited.value && newName) {
      const generatedSubdomain = generateSubdomain(newName)
      form.setFieldValue('sub_domain', generatedSubdomain)
    }
  },
)

const onSubmit = form.handleSubmit(async (values) => {
  if (isAdding.value) return
  formError.value = ''

  const payload = {
    ...values,
    phone_number: values.phone_number.replace(/\D/g, ''),
  }

  const result = await addPharmacy(payload)

  if (result) {
    router.push({ name: 'pharmacies-list' })
  } else if (!isEmptyObject(fieldErrors.value)) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof PharmacyPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to add pharmacy. Please try again.'
  }

  scrollToTop()
})

const centralDomain = computed(() => {
  return import.meta.env.VITE_TENANCY_CENTRAL_DOMAINS || window.location.host
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Add New Pharmacy</h1>
    </div>

    <Card>
      <CardContent class="pt-6">
        <form class="space-y-8" @submit="onSubmit">
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <div>
            <h2 class="text-lg font-medium mb-4">Pharmacy Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <FormField name="pharmacy_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Pharmacy Name *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter pharmacy name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="sub_domain" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Subdomain *</FormLabel>
                  <FormControl>
                    <div class="relative">
                      <Input
                        v-bind="componentField"
                        placeholder="Enter subdomain"
                        :aria-invalid="!!errorMessage"
                        class="pr-24"
                        @input="handleSubdomainInput"
                      />
                      <span
                        class="absolute inset-y-0 right-0 flex items-center pr-3 text-muted-foreground"
                      >
                        .{{ centralDomain }}
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                  <p class="text-xs text-muted-foreground mt-1">
                    Auto-generated from pharmacy name. Only lowercase letters, numbers, and hyphens
                    allowed.
                  </p>
                </FormItem>
              </FormField>
            </div>
          </div>

          <div>
            <h2 class="text-lg font-medium mb-4">Pharmacy Admin Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <FormField name="first_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>First Name *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter first name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="last_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Last Name *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter last name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="email" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Email *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="email"
                      placeholder="Enter email address"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <FormField name="phone_number" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Phone Number *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      v-maska
                      data-maska="(###) ###-####"
                      placeholder="Enter phone number"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <div class="flex justify-end gap-3">
            <Button variant="outline" type="button" :disabled="isAdding" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isAdding">
              <Loader2 v-if="isAdding" class="mr-2 h-4 w-4 animate-spin" />
              {{ isAdding ? 'Creating...' : 'Create Pharmacy' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
