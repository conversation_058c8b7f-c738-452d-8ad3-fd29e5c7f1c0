import { useAuthStore } from '@central/stores'
import type { Middleware } from './types'

export const auth: Middleware = async ({ to, next }) => {
  const authStore = useAuthStore()

  const isValid = await authStore.verifyToken()

  if (!isValid) {
    return next({
      name: 'login',
      query: { redirect: to.fullPath },
    })
  }

  return next()
}

export const guest: Middleware = async ({ next }) => {
  const authStore = useAuthStore()

  if (authStore.user) {
    return next({ name: 'dashboard' })
  }

  return next()
}
