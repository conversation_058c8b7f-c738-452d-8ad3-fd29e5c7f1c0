<script setup lang="ts">
import NavMain from '@central/components/NavMain.vue'
import NavUser from '@central/components/NavUser.vue'
import { menuItems } from '@central/config/menuItems'
import type { SidebarProps } from '@/components/ui/sidebar'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from '@/components/ui/sidebar'
import localLogo from '@central/assets/logo.png'

const appLogo = import.meta.env.VITE_SITE_LOGO || localLogo

const { open } = useSidebar()

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
})
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <div class="py-2 dark:invert dark:hue-rotate-180 dark:brightness-[175%]">
        <img v-if="open" :src="appLogo" class="max-h-12 max-w-56" alt="" />
        <img v-else src="@central/assets/small-icon.png" class="max-h-7 max-w-7" alt="" />
      </div>
    </SidebarHeader>
    <SidebarContent>
      <NavMain :menuConfig="menuItems" />
    </SidebarContent>
    <SidebarFooter>
      <NavUser />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>
