<script setup lang="ts">
import { watch } from 'vue'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Loader2 } from 'lucide-vue-next'
import { usePharmacyRequestStore } from '@central/stores/pharmacyRequestStore'
import { storeToRefs } from 'pinia'

const props = defineProps<{
  open: boolean
  paymentLogId: string
}>()

const emit = defineEmits(['update:open'])

const pharmacyRequestStore = usePharmacyRequestStore()
const { paymentLogDetail, isLoadingPaymentLogDetail, error } = storeToRefs(pharmacyRequestStore)
const { fetchPaymentLogDetail } = pharmacyRequestStore

const closeModal = () => {
  emit('update:open', false)
}

const getPaymentStatusBadge = (status: 0 | 1) => {
  switch (status) {
    case 0:
      return { variant: 'destructive' as const, text: 'Failed' }
    case 1:
      return { variant: 'default' as const, text: 'Success' }
    default:
      return { variant: 'secondary' as const, text: 'Unknown' }
  }
}

const formatDateTime = (dateStr: string, timeStr: string) => {
  if (!dateStr || !timeStr) return 'N/A'
  return `${dateStr} ${timeStr}`
}

// Watch for modal open and payment log ID changes
watch(
  () => [props.open, props.paymentLogId],
  ([isOpen, logId]) => {
    if (isOpen && logId && typeof logId === 'string') {
      fetchPaymentLogDetail(logId)
    }
  },
  { immediate: true },
)
</script>

<template>
  <Dialog :open="open" @update:open="closeModal">
    <DialogContent class="sm:max-w-[600px]">
      <DialogHeader>
        <DialogTitle>Payment Log Details</DialogTitle>
      </DialogHeader>

      <div v-if="isLoadingPaymentLogDetail" class="flex items-center justify-center py-8">
        <Loader2 class="h-6 w-6 animate-spin" />
        <span class="ml-2">Loading payment details...</span>
      </div>

      <div v-else-if="error" class="text-center py-8">
        <p class="text-destructive">{{ error }}</p>
        <Button @click="fetchPaymentLogDetail(paymentLogId)" class="mt-4" size="sm">
          Try Again
        </Button>
      </div>

      <div v-else-if="paymentLogDetail" class="space-y-6">
        <!-- Payment Status -->
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">Payment Status</h3>
          <Badge :variant="getPaymentStatusBadge(paymentLogDetail.payment_status).variant">
            {{ getPaymentStatusBadge(paymentLogDetail.payment_status).text }}
          </Badge>
        </div>

        <!-- Payment Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-muted-foreground">Payment Log ID</label>
            <p class="text-sm font-mono">{{ paymentLogDetail.id }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-muted-foreground">Plan Amount</label>
            <p class="text-sm font-semibold">${{ paymentLogDetail.plan_amount.toFixed(2) }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-muted-foreground">Transaction Type</label>
            <p class="text-sm">{{ paymentLogDetail.transaction_type }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-muted-foreground">Created Date</label>
            <p class="text-sm">
              {{ formatDateTime(paymentLogDetail.created_date, paymentLogDetail.created_time) }}
            </p>
          </div>
        </div>

        <!-- Transaction Details -->
        <div class="space-y-4">
          <h4 class="text-md font-semibold">Transaction Details</h4>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground"
                >Authorize Transaction ID</label
              >
              <p class="text-sm font-mono">
                {{ paymentLogDetail.authorize_transaction_id || 'N/A' }}
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Payment Capture ID</label>
              <p class="text-sm font-mono">{{ paymentLogDetail.payment_capture_id || 'N/A' }}</p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Authorization Expiry</label>
              <p class="text-sm">
                {{ paymentLogDetail.authorize_transaction_expired_at || 'N/A' }}
              </p>
            </div>
          </div>
        </div>

        <!-- Card Information -->
        <div class="space-y-4">
          <h4 class="text-md font-semibold">Card Information</h4>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="text-sm font-medium text-muted-foreground">Card Last 4 Digits</label>
              <p class="text-sm">
                **** **** **** {{ paymentLogDetail.last_card_four_digits || 'N/A' }}
              </p>
            </div>
            <div>
              <label class="text-sm font-medium text-muted-foreground">Card Brand</label>
              <p class="text-sm">{{ paymentLogDetail.card_brand_type || 'N/A' }}</p>
            </div>
          </div>
        </div>

        <!-- Failure Information (if applicable) -->
        <div
          v-if="paymentLogDetail.payment_status === 0 && paymentLogDetail.failed_reason"
          class="space-y-4"
        >
          <h4 class="text-md font-semibold text-destructive">Failure Information</h4>
          <div>
            <label class="text-sm font-medium text-muted-foreground">Failed Reason</label>
            <p class="text-sm text-destructive">{{ paymentLogDetail.failed_reason }}</p>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button variant="outline" @click="closeModal"> Close </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
