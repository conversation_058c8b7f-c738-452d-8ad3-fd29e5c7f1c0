export type Pharmacy = {
  id: string
  pharmacy_name: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  is_active: 0 | 1
  is_invitation_accepted: 0 | 1
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string

  // custom field
  status: boolean
}

export type PharmacyDetail = {
  id: string
  pharmacy_name: string
  email: string
  phone_number: string
  first_name: string
  last_name: string
  is_active: 0 | 1
  domain: {
    id: number
    tenant_id: string
    domain: string
  }
}

export type PharmacyListPayload = {
  searchQuery?: string
  is_active?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type PharmacyPayload = {
  pharmacy_name: string
  sub_domain: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
}
