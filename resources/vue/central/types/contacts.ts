export type ContactItem = {
  id: string
  name: string
  email: string
  phone_number: string
  product_detail: {
    main_category_name: string
    sub_category_name: string | null
    product_name: string
    product_title: string
    product_strength: string
    product_type: string
    product_qty: string
    product_form: string
    price: number
  }
  pharmacy_name: string
  created_date: string
  created_time: string
}

export type ContactListPayload = {
  searchQuery?: string
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
  pharmacy_id?: string
}

export type ContactDetails = {
  id: string
  name: string
  email: string
  phone_number: string
  product_detail: {
    main_category_name: string
    sub_category_name: string | null
    product_name: string
    product_title: string
    product_strength: string
    product_type: string
    product_qty: string
    product_form: string
    price: number
    xpedicare_url: string
    image: string
  }
  created_date: string
  created_time: string
  pharmacy_details: {
    pharmacy_name: string
    email: string
    phone_number: string
    first_name: string
    last_name: string
  }
}

export type PharmacyItem = {
  pharmacy_id: string
  pharmacy_name: string
}
