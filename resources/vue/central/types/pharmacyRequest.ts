import type { Address } from '.'

export type PharmacyItem = {
  id: string
  pharmacy_name: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  preferred_sub_domain: string
  status: 0 | 1
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string
}

export type PharmacyListPayload = {
  searchQuery?: string
  status?: '' | 0 | 1 // Empty string for all, 0 for inactive, 1 for active
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type PharmacyRequestDetail = {
  id: string
  pharmacy_name: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  preferred_sub_domain: string
  address_details: Address
  status: 0 | 1
  last_card_four_digits: string
  card_expiry_month: string
  card_expiry_year: string
  card_brand_type: string
  payment_logs: PaymentLogItem[]
}

export type PaymentLogItem = {
  id: string
  plan_amount: number
  payment_status: 0 | 1
  transaction_type: string
  last_card_four_digits: string
  card_brand_type: string
  transaction_id: string
  created_date: string
  created_time: string
}

export type PaymentLogDetail = {
  id: string
  authorize_transaction_id: string
  authorize_transaction_expired_at: string
  payment_capture_id: string | null
  plan_amount: number
  payment_status: 0 | 1
  transaction_type: string
  last_card_four_digits: string
  failed_reason: string | null
  card_brand_type: string
  created_date: string
  created_time: string
}
