import type { Address } from '.'

export type PharmacyItem = {
  id: string
  pharmacy_name: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  preferred_sub_domain: string
  created_date: string
  created_time: string
  updated_date: string
  updated_time: string
}

export type PharmacyListPayload = {
  searchQuery?: string
  page?: number
  perPage?: number
  sortByColumnName?: string
  isSortDirDesc?: 'asc' | 'desc'
}

export type PharmacyRequestDetail = {
  id: string
  pharmacy_name: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  preferred_sub_domain: string
  address_details: Address
  payment_logs: PaymentLogItem[]
  payment_card_details: PaymentCardDetail | null
}

type PaymentCardDetail = {
  card_last_4_digit: string
  card_expiry_month: string
  card_expiry_year: string
  card_brand_type: string
}

export type PaymentLogItem = {
  id: string
  plan_amount: number
  payment_status: 0 | 1
  transaction_type: string
  last_card_four_digits: string
  card_brand_type: string
  transaction_id: string
  created_date: string
  created_time: string
}
