import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@central/layouts/MainLayout.vue'
import { defineMiddleware } from '@central/middleware'
import { auth, guest } from '@central/middleware/auth'

const routes = [
  // Superadmin Login
  {
    path: '/login',
    name: 'login',
    component: () => import('@central/views/auth/LoginPage.vue'),
    beforeEnter: defineMiddleware(guest),
    meta: {
      title: 'Superadmin Login',
    },
  },

  // Superadmin Main Layout (Authenticated only)
  {
    path: '/',
    redirect: '/dashboard',
    component: MainLayout,
    beforeEnter: defineMiddleware(auth),
    children: [
      // Dashboard
      {
        path: '/dashboard',
        name: 'dashboard',
        component: () => import('@central/views/dashboard/DashboardPage.vue'),
        meta: {
          title: 'Dashboard',
        },
      },

      // Account Settings
      {
        path: '/account',
        name: 'account',
        component: () => import('@central/views/account/AccountPage.vue'),
        meta: {
          title: 'Account Settings',
        },
      },

      // Pharmacies
      {
        path: '/pharmacies',
        name: 'pharmacies-list',
        component: () => import('@central/views/pharmacy/PharmacyListPage.vue'),
        meta: {
          title: 'Pharmacies',
        },
      },
      {
        path: '/pharmacies/add',
        name: 'pharmacies-add',
        component: () => import('@central/views/pharmacy/PharmacyAddPage.vue'),
        meta: {
          title: 'Add Pharmacy',
          parent: 'Pharmacies',
          parentRouteName: 'pharmacies-list',
        },
      },
      {
        path: '/pharmacies/:id/edit',
        name: 'pharmacies-edit',
        component: () => import('@central/views/pharmacy/PharmacyEditPage.vue'),
        meta: {
          title: 'Edit Pharmacy',
          parent: 'Pharmacies',
          parentRouteName: 'pharmacies-list',
        },
      },

      // Pharmacy Onboarding Requests
      {
        path: '/pharmacies/onboarding-requests',
        name: 'pharmacy-requests-list',
        component: () => import('@central/views/pharmacyRequest/PharmacyRequestListPage.vue'),
        meta: {
          title: 'Pharmacy Onboarding Requests',
        },
      },
      {
        path: '/pharmacies/onboarding-requests/:id/details',
        name: 'pharmacy-requests-details',
        component: () => import('@central/views/pharmacyRequest/PharmacyRequestDetailsPage.vue'),
        meta: {
          title: 'Pharmacy Request Details',
          parent: 'Pharmacy Onboarding Requests',
          parentRouteName: 'pharmacy-requests-list',
        },
      },

      // Pharmacy Failed Requests
      {
        path: '/pharmacies/failed-requests',
        name: 'pharmacy-failed-list',
        component: () => import('@central/views/pharmacyFailed/PharmacyRequestListPage.vue'),
        meta: {
          title: 'Pharmacy Failed Requests',
        },
      },
      {
        path: '/pharmacies/failed-requests/:id/details',
        name: 'pharmacy-failed-details',
        component: () => import('@central/views/pharmacyFailed/PharmacyRequestDetailsPage.vue'),
        meta: {
          title: 'Pharmacy Failed Request Details',
          parent: 'Pharmacy Failed Requests',
          parentRouteName: 'pharmacy-failed-list',
        },
      },

      // plan settings
      {
        path: '/plan-settings',
        name: 'plan-settings',
        component: () => import('@central/views/plan/PlanEditPage.vue'),
        meta: {
          title: 'Plan Settings',
        },
      },
    ],
  },

  // 404 Error Page (Catch-all route)
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@central/views/error/Error404Page.vue'),
    meta: {
      title: '404 - Page Not Found',
    },
  },
]

const router = createRouter({
  history: createWebHistory('/'),
  routes: routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    } else if (from.path === to.path) {
      return { top: 0 }
    } else {
      return { top: 0 }
    }
  },
})

router.beforeEach((to, _, next) => {
  document.title = to.meta.title as string
  next()
})

export default router
