import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient } from '@central/composables'
import type { ApiResponse, PaginationResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type {
  ContactItem,
  ContactListPayload,
  ContactDetails,
  PharmacyItem,
} from '@central/types/contacts'

export const useContactsStore = defineStore('contactsStore', () => {
  const contacts = ref<ContactItem[]>([])
  const contactDetail = ref<ContactDetails | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetail = ref(false)
  const pharmacyOptions = ref<PharmacyItem[]>([])
  const isLoadingPharmacyOptions = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const selectedPharmacyId = ref<string>('')
  const searchQuery = ref('')
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  const listPayload = computed<Partial<ContactListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    pharmacy_id: selectedPharmacyId.value || undefined,
  }))

  async function fetchContactList() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { pharmacies: PaginationResponse<PharmacyItem> }
      >('/list/failed/pharmacy/registration', listPayload.value)

      if (response.data.status === 200) {
        const pagedData = response.data.pharmacies
        pharmacies.value = pagedData.records

        pagination.value = {
          currentPage: pagedData.current_page,
          perPage: pagedData.per_page,
          totalPages: pagedData.totalPage,
          totalRecords: pagedData.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch pharmacies'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacies:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacies.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function fetchPharmacyDetail(id: string) {
    isLoadingDetail.value = true
    error.value = null

    try {
      const response = await apiClient.get<
        ApiResponse & { pharmacyDetails: PharmacyRequestDetail }
      >(`/view/failed/pharmacy/registration/details/${id}`)

      if (response.data.status === 200) {
        pharmacyDetail.value = response.data.pharmacyDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch pharmacy with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacy details:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacy details.')
      return false
    } finally {
      isLoadingDetail.value = false
    }
  }

  return {
    pharmacies,
    pagination,
    isLoadingList,
    isLoadingDetail,
    error,
    fieldErrors,
    searchQuery,
    sortBy,
    pharmacyDetail,
    fetchPharmacyList,
    fetchPharmacyDetail,
  }
})
