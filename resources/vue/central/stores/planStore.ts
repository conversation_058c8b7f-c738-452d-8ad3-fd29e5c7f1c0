import { processErrors } from '@/lib'
import type { ApiResponse } from '@/types/api'
import { apiClient } from '@central/composables'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { toast } from 'vue-sonner'
import { z } from 'zod'

type PlanDetails = {
  plan_title: string
  summary: string | null
  features: string[]
  monthly_price: number
  setup_fee: number
  image: string | null
  is_active: 0 | 1
}

export const planSchema = z.object({
  plan_title: z.string().min(1, 'Plan title is required'),
  summary: z.string().optional(),
  monthly_price: z
    .union([z.string(), z.number()])
    .refine(
      (val) => {
        if (typeof val === 'string') return val.trim() !== ''
        return true
      },
      { message: 'Monthly price is required' },
    )
    .transform((val) => (typeof val === 'string' ? Number(val) : val))
    .refine((val) => !isNaN(val) && val >= 0, { message: 'Price must be non-negative' }),
  setup_fee: z
    .union([z.string(), z.number(), z.null(), z.undefined()])
    .transform((val) => {
      if (val === '' || val === undefined) return null
      if (typeof val === 'string') return Number(val)
      return val
    })
    .refine((val) => val === null || (!isNaN(val) && val >= 0), {
      message: 'Setup fee must be non-negative',
    })
    .nullable()
    .optional(),
  image: z.string().optional(),
  features: z.array(z.string().nullable()).optional(),
  is_active: z.number(),
})

export const usePlanStore = defineStore('plan', () => {
  const planDetails = ref<PlanDetails | null>(null)
  const isLoadingDetails = ref(false)
  const isUpdating = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})

  async function fetchPlan() {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { planDetails: PlanDetails }>(
        '/plan/details',
      )

      if (response.data.status === 200) {
        planDetails.value = response.data.planDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch plan details.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching plan details:', err)
      error.value = processErrors(err, 'An error occurred while fetching plan details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function updatePlan(payload: z.infer<typeof planSchema>) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/update/plan', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Plan updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update plan'
        return false
      }
    } catch (err: any) {
      console.error('Error updating plan:', err)
      error.value = processErrors(err, 'An error occurred while updating plan.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  return {
    planDetails,
    isLoadingDetails,
    isUpdating,
    error,
    fieldErrors,

    fetchPlan,
    updatePlan,
  }
})
