import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { User } from '@central/types'
import { apiClient } from '@central/composables'
import router from '@central/router'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(
    localStorage.getItem('user_data')
      ? JSON.parse(localStorage.getItem('user_data') as string)
      : null,
  )
  const isVerifying = ref(false)

  function setUser(userData: User) {
    user.value = userData
    localStorage.setItem('user_data', JSON.stringify(userData))
  }

  function clearAuth() {
    user.value = null
    localStorage.removeItem('user_data')
  }

  async function verifyToken(): Promise<boolean> {
    if (isVerifying.value) return false

    try {
      isVerifying.value = true
      const { data } = await apiClient.get('/verify-token')

      if (data.status === 200) {
        return true
      } else {
        throw data
      }
    } catch (error) {
      console.error('Token verification failed:', error)
      clearAuth()
      return false
    } finally {
      isVerifying.value = false
    }
  }

  async function logout() {
    try {
      await apiClient.get('/logout')
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      clearAuth()
      router.push({ name: 'login' })
    }
  }

  return {
    user,
    isVerifying,
    setUser,
    clearAuth,
    logout,
    verifyToken,
  }
})
