import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { apiClient } from '@central/composables'
import { toast } from 'vue-sonner'
import type { ApiResponse, PaginationResponse } from '@/types/api'
import { processErrors } from '@/lib'
import type {
  Pharmacy,
  PharmacyDetail,
  PharmacyListPayload,
  PharmacyPayload,
} from '@central/types/pharmacy'

export const usePharmacyStore = defineStore('pharmacyStore', () => {
  const pharmacies = ref<Pharmacy[]>([])
  const pharmacyDetail = ref<PharmacyDetail | null>(null)
  const isLoadingList = ref(false)
  const isLoadingDetails = ref(false)
  const isAdding = ref(false)
  const isUpdating = ref(false)
  const isTogglingStatus = ref(false)
  const isResendingInvitation = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const pagination = ref({
    currentPage: 1,
    perPage: 10,
    totalPages: 1,
    totalRecords: 0,
  })
  const searchQuery = ref('')
  const statusFilter = ref<string>('all')
  const sortBy = ref<{
    column: string
    direction: 'asc' | 'desc'
  }>({
    column: '',
    direction: 'desc',
  })

  const listPayload = computed<Partial<PharmacyListPayload>>(() => ({
    searchQuery: searchQuery.value || undefined,
    page: pagination.value.currentPage,
    perPage: pagination.value.perPage,
    sortByColumnName: sortBy.value.column || undefined,
    isSortDirDesc: sortBy.value.direction,
    is_active: statusFilter.value !== 'all' ? (Number(statusFilter.value) as unknown as 0 | 1) : '',
  }))

  async function fetchPharmacyList() {
    isLoadingList.value = true
    error.value = null

    try {
      const response = await apiClient.post<
        ApiResponse & { pharmacies: PaginationResponse<Pharmacy> }
      >('/list/pharmacy', listPayload.value)

      if (response.data.status === 200) {
        const pagedData = response.data.pharmacies
        pharmacies.value = pagedData.records.map((pharmacy) => ({
          ...pharmacy,
          status: pharmacy.is_active === 1,
        }))

        pagination.value = {
          currentPage: pagedData.current_page,
          perPage: pagedData.per_page,
          totalPages: pagedData.totalPage,
          totalRecords: pagedData.totalRecords,
        }

        return true
      } else {
        error.value = response.data.message || 'Failed to fetch pharmacies'
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacies:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacies.')
      return false
    } finally {
      isLoadingList.value = false
    }
  }

  async function getPharmacyById(id: string) {
    isLoadingDetails.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { pharmacyDetails: PharmacyDetail }>(
        `/view/pharmacy/${id}`,
      )

      if (response.data.status === 200) {
        pharmacyDetail.value = response.data.pharmacyDetails
        return true
      } else {
        error.value = response.data.message || `Failed to fetch pharmacy with ID: ${id}.`
        return false
      }
    } catch (err: any) {
      console.error('Error fetching pharmacy details:', err)
      error.value = processErrors(err, 'An error occurred while fetching pharmacy details.')
      return false
    } finally {
      isLoadingDetails.value = false
    }
  }

  async function addPharmacy(payload: PharmacyPayload) {
    isAdding.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/add/pharmacy', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy added successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to add pharmacy'
        return false
      }
    } catch (err: any) {
      console.error('Error adding pharmacy:', err)
      error.value = processErrors(err, 'An error occurred while adding pharmacy.')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function updatePharmacy(payload: PharmacyPayload & { id: string }) {
    isUpdating.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/edit/pharmacy', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy updated successfully')
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update pharmacy'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy.')
      return false
    } finally {
      isUpdating.value = false
    }
  }

  async function togglePharmacyStatus(id: string) {
    isTogglingStatus.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/update/pharmacy/status/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy status updated successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to update pharmacy status'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy status:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy status.')
      return false
    } finally {
      isTogglingStatus.value = false
    }
  }

  async function resendInvitationToPharmacy(id: string) {
    isResendingInvitation.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/resent/pharmacy/invitation/${id}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Invitation resent successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to resend invitation'
        return false
      }
    } catch (err: any) {
      console.error('Error resending invitation:', err)
      error.value = processErrors(err, 'An error occurred while resending invitation.')
      return false
    } finally {
      isResendingInvitation.value = false
    }
  }

  return {
    // State
    pharmacies,
    pharmacyDetail,
    pagination,
    isLoadingList,
    isLoadingDetails,
    isAdding,
    isUpdating,
    isTogglingStatus,
    isResendingInvitation,
    error,
    fieldErrors,
    searchQuery,
    statusFilter,
    sortBy,

    // Actions
    fetchPharmacyList,
    getPharmacyById,
    addPharmacy,
    updatePharmacy,
    togglePharmacyStatus,
    resendInvitationToPharmacy,
  }
})
