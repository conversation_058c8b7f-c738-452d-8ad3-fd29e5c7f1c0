<script setup lang="ts">
import { ref, computed } from 'vue'
import { useGlobalSettingsStore } from '@tenant/stores'
import TopBanner from './TopBanner.vue'
import { Sheet, SheetContent, SheetHeader, Sheet<PERSON>itle, SheetTrigger } from '@/components/ui/sheet'
import { storeToRefs } from 'pinia'
import { IconArrowRight, IconChevronRight } from '@tabler/icons-vue'

const props = defineProps({
  topBannerContent: {
    type: Object,
    default: () => ({ items: [] }),
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:topBannerContent'])

const store = useGlobalSettingsStore()
const { mainCategories, headerTopProducts, headerError } = storeToRefs(store)

const isSheetOpen = ref<boolean>(false)
const currentView = ref<string>('main') // 'main' or category name
const prevView = ref<string | null>(null)
const selectedCategory = ref<string | null>(null)

const showHamburgerMenu = computed(() => {
  return !headerError.value && mainCategories.value && mainCategories.value.length > 0
})

const openCategory = (categoryId: string) => {
  prevView.value = currentView.value
  selectedCategory.value = categoryId
  currentView.value = 'category'
}

const goBackToMain = () => {
  prevView.value = currentView.value
  currentView.value = 'main'
  selectedCategory.value = null
}

const onSheetOpenChange = (open: boolean) => {
  isSheetOpen.value = open
  if (!open) {
    prevView.value = currentView.value
    currentView.value = 'main'
    selectedCategory.value = null
  }
}

const transitionName = computed(() => {
  // Decide slide direction based on where we came from
  if (prevView.value === 'main' && currentView.value === 'category') return 'slide-left'
  if (prevView.value === 'category' && currentView.value === 'main') return 'slide-right'
  return 'fade'
})

const getCurrentCategory = () => {
  return mainCategories.value?.find((cat) => cat.id === selectedCategory.value)
}

const currentCategorySubCategories = computed(() => {
  const category = getCurrentCategory()
  return category?.sub_categories || []
})

const currentCategoryProducts = computed(() => {
  const category = getCurrentCategory()
  return category?.products || []
})
</script>

<template>
  <TopBanner
    :content="props.topBannerContent"
    :is-admin="props.isAdmin"
    @update:content="emit('update:topBannerContent', $event)"
  />

  <!-- navbar -->
  <nav
    class="sticky top-0 z-50 bg-gradient-to-r from-white/95 via-gray-50/95 to-white/95 backdrop-blur-sm border-b border-gray-200/30"
  >
    <!-- Main Navigation -->
    <div class="max-w-7xl mx-auto px-4">
      <div class="flex justify-between items-center py-4">
        <!-- Logo -->
        <RouterLink :to="{ name: 'Home' }" class="flex-shrink-0">
          <img
            :src="store.settings?.app_logo"
            :alt="store.settings?.app_name"
            class="max-h-12 max-w-36 sm:max-w-56"
          />
        </RouterLink>

        <!-- Right side buttons -->
        <div class="flex items-center space-x-0 sm:space-x-2">
          <!-- Get Care Button -->
          <RouterLink
            :to="{ name: 'Treatments' }"
            class="px-3 py-2 sm:px-6 sm:py-2 border border-gray-300 text-gray-700 rounded-full hover:bg-gray-50 transition-colors text-xs sm:text-base"
          >
            Get Care
          </RouterLink>

          <!-- Sheet Menu -->
          <Sheet
            v-if="showHamburgerMenu"
            v-model:open="isSheetOpen"
            @update:open="onSheetOpenChange"
          >
            <SheetTrigger as-child>
              <button class="p-2">
                <div class="w-6 h-6 flex flex-col justify-center items-center space-y-1">
                  <div
                    class="w-5 h-0.5 bg-gray-600 transition-all"
                    :class="{
                      'rotate-45 translate-y-1.5': isSheetOpen,
                    }"
                  ></div>
                  <div
                    class="w-5 h-0.5 bg-gray-600 transition-all"
                    :class="{ 'opacity-0': isSheetOpen }"
                  ></div>
                  <div
                    class="w-5 h-0.5 bg-gray-600 transition-all"
                    :class="{
                      '-rotate-45 -translate-y-1.5': isSheetOpen,
                    }"
                  ></div>
                </div>
              </button>
            </SheetTrigger>

            <SheetContent
              side="right"
              class="p-0 rounded-tl-2xl rounded-bl-2xl gap-0 sheet-content-polish [&>button:first-of-type]:p-1 [&>button:first-of-type>svg]:size-5"
            >
              <SheetHeader class="sticky top-0 bg-white flex-row items-center rounded-tl-2xl">
                <button
                  v-if="currentView === 'category'"
                  @click="goBackToMain"
                  class="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M15 19l-7-7 7-7"
                    ></path>
                  </svg>
                </button>

                <SheetTitle v-if="currentView === 'main'" class="text-xl font-medium text-gray-900">
                  Menu
                </SheetTitle>
              </SheetHeader>

              <!-- Sheet Content -->
              <div class="flex-1 overflow-y-auto">
                <transition :name="transitionName" mode="out-in">
                  <!-- Main Menu View -->
                  <div v-if="currentView === 'main'" key="main" class="px-6 py-6">
                    <!-- Explore Section -->
                    <div class="mb-8">
                      <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
                        EXPLORE
                      </h3>
                      <div class="space-y-1">
                        <button
                          v-for="category in mainCategories"
                          :key="category.id"
                          @click="openCategory(category.id)"
                          class="flex items-center justify-between w-full px-2 py-3 text-lg text-left text-gray-900 bg-gradient-to-r hover:from-amber-50 hover:via-orange-50 hover:to-amber-100 rounded-lg hover:bg-amber-50 transition-colors duration-200"
                        >
                          <div class="grid">
                            <span class="font-medium">{{ category.name }}</span>
                            <span v-if="category.gender" class="text-xs text-gray-500 font-medium">
                              {{ category.gender === 'Male' ? 'For Men' : 'For Women' }}
                            </span>
                          </div>
                          <IconChevronRight class="size-5 text-gray-400" stroke-width="2" />
                        </button>
                      </div>
                    </div>

                    <!-- Top Treatments Section -->
                    <div v-if="headerTopProducts && headerTopProducts.length > 0">
                      <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-4">
                        TOP TREATMENTS
                      </h3>
                      <div class="space-y-4">
                        <RouterLink
                          v-for="product in headerTopProducts"
                          :key="product.slug"
                          :to="{ name: 'TreatmentOverview', params: { slug: product.slug } }"
                          @click="isSheetOpen = false"
                          class="block bg-amber-50 border border-amber-100 rounded-lg p-4 hover:border-amber-200 transition-colors cursor-pointer group"
                        >
                          <div class="flex justify-between">
                            <div class="w-full flex flex-col justify-center">
                              <h4 class="font-medium text-amber-900 group-hover:text-amber-700">
                                {{ product.product_name_title }}
                              </h4>
                              <div class="text-sm text-amber-700">
                                {{ product.main_category_name }}
                              </div>
                            </div>
                            <div class="w-16 h-16 rounded-lg overflow-hidden">
                              <img
                                v-if="product.image"
                                :src="product.image"
                                :alt="product.product_name_title"
                                class="w-full h-full object-contain group-hover:scale-110 transition-transform"
                              />
                            </div>
                          </div>
                        </RouterLink>
                      </div>
                    </div>
                  </div>

                  <!-- Category View -->
                  <div v-else key="category" class="px-6 py-6">
                    <div v-if="getCurrentCategory()">
                      <div class="mb-4">
                        <h3 class="text-2xl font-semibold text-gray-900">
                          {{ getCurrentCategory()?.name }}
                        </h3>
                        <div v-if="getCurrentCategory()?.gender" class="text-sm text-gray-500">
                          {{ getCurrentCategory()?.gender === 'Male' ? 'For Men' : 'For Women' }}
                        </div>
                      </div>
                      <!-- Category Header with Image -->
                      <div class="mb-6">
                        <RouterLink
                          :to="{
                            name: 'Treatments',
                            params: { category_slug: getCurrentCategory()?.slug },
                          }"
                          @click="isSheetOpen = false"
                          class="group"
                        >
                          <div
                            class="bg-gradient-to-r from-amber-50 via-orange-50 to-amber-100 border border-amber-100 rounded-lg p-6 mb-4"
                          >
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">
                              Explore {{ getCurrentCategory()?.name }} treatments
                            </h3>
                            <div class="grid grid-cols-4">
                              <div class="col-span-1 self-end">
                                <button
                                  class="block w-full bg-transparent text-black border border-gray-500 py-2 rounded-full font-medium group-hover:bg-gray-700 group-hover:text-white transition-colors text-center"
                                >
                                  <IconArrowRight class="w-5 h-5 inline-block" />
                                </button>
                              </div>
                              <div class="col-span-3 w-36 h-32 ms-auto">
                                <img
                                  :src="getCurrentCategory()?.image"
                                  :alt="getCurrentCategory()?.name"
                                  class="w-full h-full object-contain"
                                />
                              </div>
                            </div>
                          </div>
                        </RouterLink>
                      </div>

                      <!-- Sub-Categories Section -->
                      <div v-if="currentCategorySubCategories.length > 0" class="mb-6">
                        <h4
                          class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3"
                        >
                          EXPLORE
                        </h4>
                        <div class="space-y-1">
                          <template
                            v-for="subCategory in currentCategorySubCategories"
                            :key="subCategory.id"
                          >
                            <RouterLink
                              :to="{
                                name: 'Treatments',
                                params: {
                                  category_slug: getCurrentCategory()?.slug,
                                  sub_category_slug: subCategory.slug,
                                },
                              }"
                              @click="isSheetOpen = false"
                            >
                              <div
                                class="flex items-center justify-between w-full px-2 py-3 text-xl text-left text-gray-900 bg-gradient-to-r hover:from-amber-50 hover:via-orange-50 hover:to-amber-100 rounded-lg hover:bg-amber-50 transition-colors duration-200"
                              >
                                <span class="font-medium">{{ subCategory.name }}</span>
                                <IconChevronRight class="size-6 text-gray-400" stroke-width="2" />
                              </div>
                            </RouterLink>
                          </template>
                        </div>
                      </div>

                      <!-- Products Section -->
                      <div v-if="currentCategoryProducts.length > 0" class="mb-6">
                        <h4
                          class="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-3"
                        >
                          PRODUCTS
                        </h4>
                        <div class="space-y-2">
                          <template v-for="product in currentCategoryProducts" :key="product.slug">
                            <RouterLink
                              :to="{ name: 'TreatmentOverview', params: { slug: product.slug } }"
                              @click="isSheetOpen = false"
                            >
                              <div
                                class="px-2 text-gray-900 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-between py-2.5"
                              >
                                <span>{{ product.product_name_title }}</span>
                                <IconChevronRight class="size-5 text-gray-400" stroke-width="2" />
                              </div>
                            </RouterLink>
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                </transition>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </div>
  </nav>
</template>

<style scoped>
.sheet-content-polish {
  box-shadow: -10px 20px 40px rgba(15, 23, 42, 0.06);
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.98), #ffffff);
}

/* Slide left: main -> category */
.slide-left-enter-active,
.slide-left-leave-active {
  transition:
    transform 260ms cubic-bezier(0.2, 0.9, 0.3, 1),
    opacity 200ms ease;
}
.slide-left-enter-from {
  transform: translateX(20px);
  opacity: 0;
}
.slide-left-enter-to {
  transform: translateX(0);
  opacity: 1;
}
.slide-left-leave-from {
  transform: translateX(0);
  opacity: 1;
}
.slide-left-leave-to {
  transform: translateX(-12px);
  opacity: 0;
}

/* Slide right: category -> main */
.slide-right-enter-active,
.slide-right-leave-active {
  transition:
    transform 260ms cubic-bezier(0.2, 0.9, 0.3, 1),
    opacity 200ms ease;
}
.slide-right-enter-from {
  transform: translateX(-20px);
  opacity: 0;
}
.slide-right-enter-to {
  transform: translateX(0);
  opacity: 1;
}
.slide-right-leave-from {
  transform: translateX(0);
  opacity: 1;
}
.slide-right-leave-to {
  transform: translateX(12px);
  opacity: 0;
}

/* Fallback fade */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 160ms ease;
}
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
.fade-enter-to,
.fade-leave-from {
  opacity: 1;
}
</style>
