<script setup lang="ts">
import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  IconBrandFacebook,
  IconBrandGmail,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandThreads,
  IconBrandTiktok,
  IconBrandX,
  IconBrandYoutube,
  type Icon as TablerIcon,
} from '@tabler/icons-vue'

defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['close', 'select'])

const iconComponents: Record<string, TablerIcon> = {
  IconBrandFacebook,
  IconBrandInstagram,
  IconBrandTiktok,
  IconBrandX,
  IconBrandLinkedin,
  IconBrandThreads,
  IconBrandYoutube,
  IconBrandGmail,
}

const availableIcons = Object.keys(iconComponents)

const closeModal = () => {
  emit('close')
}

const selectIcon = (iconName: string) => {
  emit('select', iconName)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Select an Icon</DialogTitle>
      </DialogHeader>
      <div class="grid grid-cols-6 gap-4 py-4">
        <div
          v-for="iconName in availableIcons"
          :key="iconName"
          class="p-2 border rounded-lg flex items-center justify-center cursor-pointer hover:bg-accent"
          @click="selectIcon(iconName)"
        >
          <component :is="iconComponents[iconName]" class="h-8 w-8" />
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
