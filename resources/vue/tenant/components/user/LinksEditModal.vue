<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { IconPlus, IconTrash } from '@tabler/icons-vue'

type Link = { title: string; href: string; newTab: boolean }

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  links: {
    type: Array as () => Link[],
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editableLinks = ref<Link[]>([])

watch(
  () => props.isOpen,
  (newVal) => {
    if (newVal) {
      editableLinks.value = JSON.parse(JSON.stringify(props.links || []))
    }
  },
  { immediate: true },
)

const addLink = () => {
  editableLinks.value.push({ title: '', href: '', newTab: false })
}

const removeLink = (index: number) => {
  editableLinks.value.splice(index, 1)
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', editableLinks.value)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Edit Links</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
        <div
          v-for="(link, index) in editableLinks"
          :key="index"
          class="flex items-start gap-2 p-4 border rounded-lg"
        >
          <div class="grid gap-2 flex-1">
            <Label :for="`title-${index}`">Title</Label>
            <Input :id="`title-${index}`" v-model="link.title" />
            <Label :for="`href-${index}`">URL</Label>
            <Input :id="`href-${index}`" v-model="link.href" />
            <div class="flex items-center gap-2 mt-2">
              <Checkbox :id="`newTab-${index}`" v-model="link.newTab" />
              <Label :for="`newTab-${index}`">Open in new tab</Label>
            </div>
          </div>
          <Button variant="destructive" size="icon" @click="removeLink(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>
        <Button variant="outline" @click="addLink">
          <IconPlus class="w-4 h-4 mr-2" />
          Add Link
        </Button>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
