<script setup lang="ts">
import { ref, nextTick } from 'vue'
import EditButton from '@tenant/components/user/EditButton.vue'
import ButtonEditModal from '@tenant/components/user/ButtonEditModal.vue'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const titleRef = ref<HTMLElement | null>(null)
const subtitleRef = ref<HTMLElement | null>(null)
const paragraphRef = ref<HTMLElement | null>(null)
const isButtonModalOpen = ref(false)

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerText) {
    const newContent = { ...props.content, [field]: target.innerText }
    emit('update:content', newContent)
  }
}

const onButtonSave = (newButtonData: { text: string; href: string }) => {
  const newContent = {
    ...props.content,
    buttonText: newButtonData.text,
    buttonLink: newButtonData.href,
  }
  emit('update:content', newContent)
}
</script>

<template>
  <!-- CTA Section -->
  <section
    class="relative py-28 bg-gradient-to-br from-amber-50 via-white to-amber-100 overflow-hidden"
  >
    <!-- Animated background accent -->
    <div
      class="absolute -top-32 left-1/2 -translate-x-1/2 w-[700px] h-[700px] bg-amber-200/20 rounded-full blur-3xl animate-pulse-slow z-0"
    ></div>
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
      <div class="relative group inline-block">
        <h2
          ref="titleRef"
          class="text-4xl lg:text-5xl font-extrabold text-neutral-900 mb-4 animate-fade-in"
          @blur="onTextBlur('title', $event)"
        >
          {{ content.title || 'Type here...' }}
        </h2>
        <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
      </div>
      <div class="relative group">
        <p
          ref="subtitleRef"
          class="text-2xl text-amber-600 font-semibold mb-4 animate-fade-in delay-100"
          @blur="onTextBlur('subtitle', $event)"
        >
          {{ content.subtitle || 'Type here...' }}
        </p>
        <EditButton :is-admin="isAdmin" @edit="makeEditable(subtitleRef)" />
      </div>
      <div class="relative group max-w-2xl mx-auto">
        <p
          ref="paragraphRef"
          class="text-xl text-neutral-700 mb-12 leading-relaxed animate-fade-in delay-200"
          @blur="onTextBlur('paragraph', $event)"
        >
          {{ content.paragraph || 'Type here...' }}
        </p>
        <EditButton :is-admin="isAdmin" @edit="makeEditable(paragraphRef)" />
      </div>
      <div class="flex flex-col sm:flex-row gap-6 justify-center animate-fade-in delay-300">
        <div class="relative group inline-block">
          <a
            v-if="(content.buttonLink as string)?.includes('http')"
            :href="content.buttonLink"
            class="bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg cursor-pointer"
          >
            {{ content.buttonText }}
          </a>
          <RouterLink
            v-else
            :to="content.buttonLink"
            class="bg-black text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-amber-500 hover:text-white transition-all duration-200 shadow-lg cursor-pointer"
          >
            {{ content.buttonText }}
          </RouterLink>
          <EditButton :is-admin="isAdmin" @edit="isButtonModalOpen = true" />
        </div>
      </div>
    </div>
    <ButtonEditModal
      :is-open="isButtonModalOpen"
      :text="content.buttonText"
      :href="content.buttonLink"
      @close="isButtonModalOpen = false"
      @save="onButtonSave"
    />
  </section>
</template>

<style scoped>
@keyframes pulse-slow {
  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
