<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { IconPlus, IconTrash } from '@tabler/icons-vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  phrases: {
    type: Array as () => string[],
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editablePhrases = ref([...props.phrases])

watch(
  () => props.phrases,
  (newVal) => {
    editablePhrases.value = [...newVal]
  },
)

const addPhrase = () => {
  editablePhrases.value.push('')
}

const removePhrase = (index: number) => {
  editablePhrases.value.splice(index, 1)
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit(
    'save',
    editablePhrases.value.filter((phrase) => phrase.length > 0),
  )
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Edit Phrases</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4">
        <div v-for="(_, index) in editablePhrases" :key="index" class="flex items-center gap-2">
          <Input v-model="editablePhrases[index]" />
          <Button
            v-if="editablePhrases.length > 1"
            variant="destructive"
            size="icon"
            @click="removePhrase(index)"
          >
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>
        <Button variant="outline" @click="addPhrase">
          <IconPlus class="w-4 h-4 mr-2" />
          Add Phrase
        </Button>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
