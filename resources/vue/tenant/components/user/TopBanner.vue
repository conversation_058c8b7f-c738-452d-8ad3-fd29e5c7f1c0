<script setup lang="ts">
import { ref, computed } from 'vue'
import EditButton from '@tenant/components/user/EditButton.vue'
import TopBannerEditModal from '@tenant/components/user/TopBannerEditModal.vue'
import { Button } from '@/components/ui/button'
import { IconPlus } from '@tabler/icons-vue'

const props = defineProps({
  content: {
    type: Object,
    default: () => ({ items: [] }),
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const isModalOpen = ref(false)

const bannerItems = computed(() => {
  return props.content?.items?.length > 0 ? props.content.items : []
})

const animationDuration = computed(() => {
  const itemCount = bannerItems.value.length
  if (itemCount <= 3) return 15
  if (itemCount <= 6) return 25
  if (itemCount <= 10) return 35
  return 45
})

const onSave = (items: any[]) => {
  const newContent = { ...props.content, items }
  emit('update:content', newContent)
}

const addBannerItems = () => {
  isModalOpen.value = true
}
</script>

<template>
  <div
    v-if="bannerItems.length > 0"
    class="bg-gradient-to-r from-gray-100 via-stone-100 to-gray-100 border-b border-gray-200/50 overflow-hidden py-2 z-50 relative group"
  >
    <div class="relative">
      <div
        class="flex animate-ticker whitespace-nowrap"
        :style="{
          '--animation-duration': `${animationDuration}s`,
          '--item-spacing': bannerItems.length <= 5 ? '7rem' : '4rem',
        }"
      >
        <!-- Render items multiple times for seamless infinite scroll -->
        <div
          v-for="setIndex in 3"
          :key="`set-${setIndex}`"
          class="flex items-center flex-shrink-0"
          :class="bannerItems.length <= 5 ? 'space-x-28' : 'space-x-16'"
          :style="{
            paddingRight: bannerItems.length <= 5 ? '7rem' : '4rem',
          }"
        >
          <div
            v-for="(item, index) in bannerItems"
            :key="`set${setIndex}-${index}`"
            class="flex items-center space-x-2 flex-shrink-0"
          >
            <svg
              v-if="item.icon"
              class="w-4 h-4 text-gray-600 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="1.5"
                :d="item.icon"
              ></path>
            </svg>
            <span class="text-xs font-medium text-gray-700">{{ item.text }}</span>
          </div>
        </div>
      </div>
    </div>
    <EditButton :is-admin="isAdmin" @edit="isModalOpen = true" class="bottom-0 top-0 right-0" />
  </div>

  <div
    v-else-if="isAdmin && (!content?.items || content.items.length === 0)"
    class="bg-gradient-to-r from-gray-100 via-stone-100 to-gray-100 border-b border-gray-200/50 py-4 z-50 relative group"
  >
    <div class="flex justify-center">
      <Button variant="outline" @click="addBannerItems" class="text-sm">
        <IconPlus class="w-4 h-4 mr-2" />
        Add Banner Items
      </Button>
    </div>
  </div>

  <TopBannerEditModal
    :is-open="isModalOpen"
    :items="content?.items || []"
    @close="isModalOpen = false"
    @save="onSave"
  />
</template>

<style scoped>
@keyframes ticker {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-33.333%); /* Move by 1/3 since we have 3 sets */
  }
}

.animate-ticker {
  animation: ticker var(--animation-duration, 30s) linear infinite;
}

.group:hover .animate-ticker {
  animation-play-state: paused;
}

@media screen and (max-width: 640px) {
  .animate-ticker {
    animation-duration: calc(var(--animation-duration, 30s) * 0.7);
  }
}

@media screen and (min-width: 1024px) {
  .animate-ticker {
    animation-duration: calc(var(--animation-duration, 30s) * 1.3);
  }
}
</style>
