<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { IconPlus, IconTrash, IconEdit } from '@tabler/icons-vue'
import IconEditModal from '@tenant/components/user/IconEditModal.vue'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'

type BannerItem = { text: string; icon: string }

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  items: {
    type: Array as () => BannerItem[],
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editableItems = ref(JSON.parse(JSON.stringify(props.items)))
const isIconModalOpen = ref(false)
const editingItemIndex = ref<number | null>(null)

watch(
  () => props.items,
  (newVal) => {
    editableItems.value = JSON.parse(JSON.stringify(newVal))
  },
)

const addItem = () => {
  editableItems.value.push({ text: '', icon: '' })
}

const removeItem = (index: number) => {
  editableItems.value.splice(index, 1)
}

const openIconModal = (index: number) => {
  editingItemIndex.value = index
  isIconModalOpen.value = true
}

const onIconSave = (newIcon: string) => {
  if (editingItemIndex.value !== null) {
    editableItems.value[editingItemIndex.value].icon = newIcon
  }
  isIconModalOpen.value = false
  editingItemIndex.value = null
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', editableItems.value)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Edit Top Banner Items</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
        <Alert class="bg-amber-100 mb-3">
          <AlertTitle>Heads up!</AlertTitle>
          <AlertDescription>
            <ul class="list-disc pl-5">
              <li>Keep minimum 3 items and maximum 10 items for better user experience.</li>
              <li>Remove all items to disable the top banner.</li>
            </ul>
          </AlertDescription>
        </Alert>
        <div
          v-for="(item, index) in editableItems"
          :key="index"
          class="flex items-start gap-2 p-4 border rounded-lg"
        >
          <div class="grid gap-2 flex-1">
            <Label :for="`text-${index}`">Text</Label>
            <Input :id="`text-${index}`" v-model="item.text" placeholder="Enter banner text" />
            <Label>Icon</Label>
            <div class="flex items-center gap-2">
              <div class="flex items-center justify-center w-10 h-10 border rounded-lg bg-gray-50">
                <svg
                  v-if="item.icon"
                  class="w-5 h-5 text-gray-700"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="1.5"
                    :d="item.icon"
                  ></path>
                </svg>
                <span v-else class="text-xs text-gray-400">No icon</span>
              </div>
              <Button variant="outline" size="sm" @click="openIconModal(index)">
                <IconEdit class="w-4 h-4 mr-1" />
                Select Icon
              </Button>
            </div>
          </div>
          <Button variant="destructive" size="icon" @click="removeItem(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>
        <Button variant="outline" @click="addItem">
          <IconPlus class="w-4 h-4 mr-2" />
          Add Banner Item
        </Button>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <IconEditModal
    :is-open="isIconModalOpen"
    :icon="editingItemIndex !== null ? editableItems[editingItemIndex].icon : ''"
    @close="isIconModalOpen = false"
    @save="onIconSave"
  />
</template>
