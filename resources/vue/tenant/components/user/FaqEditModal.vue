<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { IconPlus, IconTrash } from '@tabler/icons-vue'

type Faq = { question: string; answer: string }

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  faqs: {
    type: Array as () => Faq[],
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editableFaqs = ref(JSON.parse(JSON.stringify(props.faqs)))

watch(
  () => props.faqs,
  (newVal) => {
    editableFaqs.value = JSON.parse(JSON.stringify(newVal))
  },
)

const addFaq = () => {
  editableFaqs.value.push({ question: '', answer: '' })
}

const removeFaq = (index: number) => {
  editableFaqs.value.splice(index, 1)
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit(
    'save',
    editableFaqs.value.filter((faq: Faq) => faq.question.length > 0 && faq.answer.length > 0),
  )
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Edit FAQs</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
        <div
          v-for="(faq, index) in editableFaqs"
          :key="index"
          class="flex items-start gap-2 p-4 border rounded-lg"
        >
          <div class="grid gap-2 flex-1">
            <Label :for="`question-${index}`">Question</Label>
            <Input :id="`question-${index}`" v-model="faq.question" />
            <Label :for="`answer-${index}`">Answer</Label>
            <Textarea :id="`answer-${index}`" v-model="faq.answer" />
          </div>
          <Button variant="destructive" size="icon" @click="removeFaq(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>
        <Button variant="outline" @click="addFaq">
          <IconPlus class="w-4 h-4 mr-2" />
          Add FAQ
        </Button>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
