<script setup lang="ts">
import { ref, nextTick } from 'vue'
import ProductItem from './ProductItem.vue'
import EditButton from '@tenant/components/user/EditButton.vue'
import ButtonEditModal from '@tenant/components/user/ButtonEditModal.vue'
import { useGlobalSettingsStore } from '@tenant/stores'
import { storeToRefs } from 'pinia'

const props = defineProps({
  content: {
    type: Object,
    required: true,
  },
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:content'])

const globalStore = useGlobalSettingsStore()
const { topProducts } = storeToRefs(globalStore)

const titleRef = ref<HTMLElement | null>(null)
const isButtonModalOpen = ref(false)

const makeEditable = async (el: HTMLElement | null) => {
  if (el) {
    el.contentEditable = 'true'
    await nextTick()
    el.focus()
  }
}

const onTextBlur = (field: string, event: FocusEvent) => {
  const target = event.target as HTMLElement
  target.contentEditable = 'false'
  if (props.content[field] !== target.innerText) {
    const newContent = { ...props.content, [field]: target.innerText }
    emit('update:content', newContent)
  }
}

const onButtonSave = (newButtonData: { text: string; href: string }) => {
  const newContent = {
    ...props.content,
    buttonText: newButtonData.text,
    buttonLink: newButtonData.href,
  }
  emit('update:content', newContent)
}
</script>

<template>
  <!-- Product Highlights -->
  <section v-if="topProducts.length" class="py-24 bg-neutral-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="w-full flex justify-center items-center mb-10 sm:mb-20">
        <div class="relative group">
          <h2
            ref="titleRef"
            class="text-4xl lg:text-5xl font-bold text-neutral-900 flex-1 text-center"
            @blur="onTextBlur('title', $event)"
          >
            {{ content.title || 'Top Products' }}
          </h2>
          <EditButton :is-admin="isAdmin" @edit="makeEditable(titleRef)" />
        </div>
      </div>

      <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        <ProductItem v-for="product in topProducts" :key="product.slug" :product="product" />
      </div>
      <div class="mt-20 text-center">
        <div class="relative group inline-block">
          <a
            v-if="(content.buttonLink as string)?.includes('http')"
            :href="content.buttonLink"
            class="bg-neutral-900 text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-200 font-medium cursor-pointer"
          >
            {{ content.buttonText }}
          </a>
          <RouterLink
            v-else
            :to="content.buttonLink"
            class="bg-neutral-900 text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-200 font-medium cursor-pointer"
          >
            {{ content.buttonText }}
          </RouterLink>
          <EditButton :is-admin="isAdmin" @edit="isButtonModalOpen = true" />
        </div>
      </div>
    </div>
    <ButtonEditModal
      :is-open="isButtonModalOpen"
      :text="content.buttonText"
      :href="content.buttonLink"
      @close="isButtonModalOpen = false"
      @save="onButtonSave"
    />
  </section>
</template>
