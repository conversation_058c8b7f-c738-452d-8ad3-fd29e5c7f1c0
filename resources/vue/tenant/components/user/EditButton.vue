<script setup lang="ts">
import { IconEdit } from '@tabler/icons-vue'

defineProps({
  isAdmin: {
    type: Boolean,
    default: false,
  },
})

defineEmits(['edit'])
</script>

<template>
  <button
    v-if="isAdmin"
    @click.stop.prevent="$emit('edit')"
    class="absolute -top-2 -right-2 bg-gray-800 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity z-50"
  >
    <IconEdit class="w-4 h-4" />
  </button>
</template>
