<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { IconPlus, IconTrash } from '@tabler/icons-vue'

type Link = { title: string; href: string; newTab: boolean }
type PolicyData = { links: Link[]; copyright: string }

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  data: {
    type: Object as () => PolicyData,
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const editableData = ref<PolicyData>({ links: [], copyright: '' })

watch(
  () => props.data,
  (newData) => {
    editableData.value = JSON.parse(JSON.stringify(newData || { links: [], copyright: '' }))
  },
  { immediate: true, deep: true },
)

const addLink = () => {
  editableData.value.links.push({ title: '', href: '', newTab: false })
}

const removeLink = (index: number) => {
  editableData.value.links.splice(index, 1)
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', editableData.value)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent class="sm:max-w-[650px]">
      <DialogHeader>
        <DialogTitle>Edit Policy & Copyright</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
        <!-- Policy URLs Section -->
        <div class="space-y-4">
          <h3 class="text-lg font-medium">Policy URLs</h3>
          <div
            v-for="(link, index) in editableData.links"
            :key="index"
            class="flex items-start gap-2 p-4 border rounded-lg"
          >
            <div class="grid gap-2 flex-1">
              <Label :for="`title-${index}`">Title</Label>
              <Input :id="`title-${index}`" v-model="link.title" />
              <Label :for="`href-${index}`">URL</Label>
              <Input :id="`href-${index}`" v-model="link.href" />
              <div class="flex items-center gap-2 mt-2">
                <Checkbox :id="`newTab-${index}`" v-model="link.newTab" />
                <Label :for="`newTab-${index}`">Open in new tab</Label>
              </div>
            </div>
            <Button variant="destructive" size="icon" @click="removeLink(index)">
              <IconTrash class="w-4 h-4" />
            </Button>
          </div>
          <Button variant="outline" @click="addLink">
            <IconPlus class="w-4 h-4 mr-2" />
            Add Policy Link
          </Button>
        </div>

        <!-- Separator -->
        <div class="border-t my-4"></div>

        <!-- Copyright Section -->
        <div class="space-y-2">
          <h3 class="text-lg font-medium">Copyright Text</h3>
          <Input v-model="editableData.copyright" />
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
