<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  data: {
    type: Object as () => Record<string, any>,
    required: true,
  },
  title: {
    type: String,
    default: 'Edit Details',
  },
})

const emit = defineEmits(['close', 'save'])

const editableData = ref(props.data ? JSON.parse(JSON.stringify(props.data)) : {})

watch(
  () => props.data,
  (newVal) => {
    editableData.value = newVal ? JSON.parse(JSON.stringify(newVal)) : {}
  },
)

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', editableData.value)
  closeModal()
}

const formatLabel = (key: string) => {
  return key.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>{{ title }}</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4">
        <div
          v-for="(key, index) in Object.keys(editableData)"
          :key="index"
          class="grid grid-cols-4 items-center gap-4"
        >
          <Label :for="`prop-${index}`" class="text-right">
            {{ formatLabel(key) }}
          </Label>
          <Input :id="`prop-${index}`" v-model="editableData[key]" class="col-span-3" />
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
