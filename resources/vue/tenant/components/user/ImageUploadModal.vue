<script setup lang="ts">
import { ref } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'

defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const isDragging = ref(false)
const previewUrl = ref<string | null>(null)
const fileInput = ref<HTMLInputElement | null>(null)

const onDragOver = () => {
  isDragging.value = true
}

const onDragLeave = () => {
  isDragging.value = false
}

const onDrop = (event: DragEvent) => {
  isDragging.value = false
  const file = event.dataTransfer?.files[0]
  if (file) {
    handleFile(file)
  }
}

const onFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    handleFile(file)
  }
}

const handleFile = (file: File) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    previewUrl.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const triggerFileInput = () => {
  fileInput.value?.click()
}

const closeModal = () => {
  previewUrl.value = null
  emit('close')
}

const saveImage = () => {
  if (previewUrl.value) {
    emit('save', previewUrl.value)
    closeModal()
  }
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent>
      <DialogHeader>
        <DialogTitle>Upload Image</DialogTitle>
        <DialogDescription> Drag and drop an image here or click to select one. </DialogDescription>
      </DialogHeader>
      <div
        class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center"
        @dragover.prevent="onDragOver"
        @dragleave.prevent="onDragLeave"
        @drop.prevent="onDrop"
        :class="{ 'border-blue-500': isDragging }"
      >
        <div v-if="!previewUrl">
          <p>Drag & Drop your image here</p>
          <p class="my-2">or</p>
          <Button @click="triggerFileInput">Select File</Button>
          <input
            type="file"
            ref="fileInput"
            @change="onFileSelect"
            class="hidden"
            accept="image/*"
          />
        </div>
        <div v-else>
          <img :src="previewUrl" class="max-h-64 mx-auto" />
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="saveImage" :disabled="!previewUrl">Save</Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
