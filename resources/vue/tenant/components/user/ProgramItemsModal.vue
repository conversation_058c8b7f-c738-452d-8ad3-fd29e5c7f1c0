<script setup lang="ts">
import type { ProgramProductItem } from '@tenant/types'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { capitalize, formatCurrency } from '@/lib'
import PreVisitModal from './PreVisitModal.vue'
import { ref } from 'vue'

defineProps<{
  open: boolean
  productName: string
  items: ProgramProductItem[]
}>()

const emit = defineEmits(['update:open'])

const isPreVisitModalOpen = ref(false)
const selectedProductItemId = ref('')
const selectedXpedicareUrl = ref('')

const closeModal = () => {
  emit('update:open', false)
}

const openPreVisitModal = (productItemId: string, xpedicareUrl: string) => {
  selectedProductItemId.value = productItemId
  selectedXpedicareUrl.value = xpedicareUrl
  isPreVisitModalOpen.value = true
}
</script>

<template>
  <Dialog :open="open" @update:open="closeModal">
    <DialogContent class="sm:max-w-[625px]">
      <DialogHeader>
        <DialogTitle>Available in {{ productName }}</DialogTitle>
        <DialogDescription> Choose one of the following options to get started. </DialogDescription>
      </DialogHeader>
      <div class="grid gap-4 py-4 overflow-y-auto max-h-[400px]">
        <div
          v-for="item in items"
          :key="item.product_id"
          class="flex flex-col sm:flex-row justify-between sm:items-end p-4 border rounded-lg gap-3 sm:gap-6"
        >
          <div>
            <h4 class="font-semibold">{{ item.product_name }}</h4>
            <div class="text-sm text-neutral-600 mt-1 flex flex-wrap gap-2">
              <span
                v-if="item.product_form"
                class="px-2.5 py-0.5 bg-white/70 text-gray-800 border border-neutral-200 rounded-full text-xs font-medium text-wrap break-words"
                >{{ capitalize(item.product_form) }}</span
              >
              <span
                v-if="item.product_strength"
                class="px-2.5 py-0.5 bg-white/70 text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                >{{ item.product_strength }}</span
              >
            </div>
            <div class="text-sm font-medium mt-3">{{ formatCurrency(item.price) }}</div>
          </div>
          <div>
            <button
              @click="openPreVisitModal(item.product_item_id, item.xpedicare_url)"
              class="bg-black text-white px-4 py-2 rounded-full hover:bg-neutral-800 transition-all duration-200 font-medium cursor-pointer text-center text-sm"
            >
              Get&nbsp;Started
            </button>
          </div>
        </div>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal"> Close </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <PreVisitModal
    :open="isPreVisitModalOpen"
    :product-item-id="selectedProductItemId"
    :xpedicare-url="selectedXpedicareUrl"
    @update:open="isPreVisitModalOpen = $event"
  />
</template>
