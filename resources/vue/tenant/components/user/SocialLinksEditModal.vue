<script setup lang="ts">
import { ref, watch } from 'vue'
import { Button } from '@/components/ui/button'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { IconPlus, IconTrash, IconX, type Icon as TablerIcon } from '@tabler/icons-vue'
import IconPickerDialog from '@tenant/components/user/IconPickerDialog.vue'
import {
  IconBrandFacebook,
  IconBrandGmail,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandThreads,
  IconBrandTiktok,
  IconBrandX,
  IconBrandYoutube,
} from '@tabler/icons-vue'

type Link = { title: string; href: string; icon: string; newTab: boolean }

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true,
  },
  links: {
    type: Array as () => Link[],
    required: true,
  },
})

const emit = defineEmits(['close', 'save'])

const iconComponents: Record<string, TablerIcon> = {
  IconBrandFacebook,
  IconBrandGmail,
  IconBrandInstagram,
  IconBrandLinkedin,
  IconBrandThreads,
  IconBrandTiktok,
  IconBrandX,
  IconBrandYoutube,
}

const editableLinks = ref<Link[]>([])
const isIconPickerOpen = ref(false)
const editingLinkIndex = ref<number | null>(null)

watch(
  () => props.isOpen,
  (val) => {
    if (val) {
      editableLinks.value = JSON.parse(JSON.stringify(props.links || []))
    }
  },
  { immediate: true },
)

const addLink = () => {
  editableLinks.value.push({ title: '', href: '', icon: '', newTab: false })
}

const removeLink = (index: number) => {
  editableLinks.value.splice(index, 1)
}

const openIconPicker = (index: number) => {
  editingLinkIndex.value = index
  isIconPickerOpen.value = true
}

const onIconSelect = (icon: string) => {
  if (editingLinkIndex.value !== null) {
    editableLinks.value[editingLinkIndex.value].icon = icon
  }
}

const clearIcon = (index: number) => {
  editableLinks.value[index].icon = ''
}

const closeModal = () => {
  emit('close')
}

const save = () => {
  emit('save', editableLinks.value)
  closeModal()
}
</script>

<template>
  <Dialog :open="isOpen" @update:open="closeModal">
    <DialogContent class="max-w-3xl">
      <DialogHeader>
        <DialogTitle>Edit Social Links</DialogTitle>
      </DialogHeader>
      <div class="grid gap-4 py-4 max-h-[60vh] overflow-y-auto">
        <div
          v-for="(link, index) in editableLinks"
          :key="index"
          class="flex items-start gap-2 p-4 border rounded-lg"
        >
          <div class="grid gap-2 flex-1">
            <Label :for="`title-${index}`">Title</Label>
            <Input :id="`title-${index}`" v-model="link.title" />
            <Label :for="`href-${index}`">URL</Label>
            <Input :id="`href-${index}`" v-model="link.href" />
            <Label>Icon</Label>
            <div class="flex items-center gap-2">
              <div
                class="w-20 h-10 border rounded-md flex items-center justify-center bg-gray-100 dark:bg-gray-800"
              >
                <component v-if="link.icon" :is="iconComponents[link.icon]" class="w-6 h-6" />
                <span v-else class="text-xs text-gray-500">None</span>
              </div>
              <Button variant="outline" @click="openIconPicker(index)">Select Icon</Button>
              <Button variant="ghost" size="icon" @click="clearIcon(index)">
                <IconX class="w-4 h-4" />
              </Button>
            </div>
            <div class="flex items-center gap-2 mt-2">
              <Checkbox :id="`newTab-${index}`" v-model="link.newTab" />
              <Label :for="`newTab-${index}`">Open in new tab</Label>
            </div>
          </div>
          <Button variant="destructive" size="icon" @click="removeLink(index)">
            <IconTrash class="w-4 h-4" />
          </Button>
        </div>
        <Button variant="outline" @click="addLink">
          <IconPlus class="w-4 h-4 mr-2" />
          Add Link
        </Button>
      </div>
      <DialogFooter>
        <Button variant="outline" @click="closeModal">Cancel</Button>
        <Button @click="save">Save</Button>
      </DialogFooter>
    </DialogContent>
    <IconPickerDialog
      :is-open="isIconPickerOpen"
      @close="isIconPickerOpen = false"
      @select="onIconSelect"
    />
  </Dialog>
</template>
