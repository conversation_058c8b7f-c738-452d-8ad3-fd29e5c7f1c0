<script setup lang="ts">
import NavMain from '@tenant/components/NavMain.vue'
import NavUser from '@tenant/components/NavUser.vue'
import { menuItems } from '@tenant/config/menuItems'
import type { SidebarProps } from '@/components/ui/sidebar'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarRail,
  useSidebar,
} from '@/components/ui/sidebar'
import { useGlobalSettingsStore } from '@tenant/stores'

const globalStore = useGlobalSettingsStore()

const { open } = useSidebar()

const props = withDefaults(defineProps<SidebarProps>(), {
  collapsible: 'icon',
})
</script>

<template>
  <Sidebar v-bind="props">
    <SidebarHeader>
      <div class="py-2 dark:invert dark:hue-rotate-180 dark:brightness-[175%]">
        <img v-if="open" :src="globalStore.settings?.app_logo" class="max-h-12 max-w-56" alt="" />
        <img v-else :src="globalStore.settings?.app_favicon" class="max-h-7 max-w-7" alt="" />
      </div>
    </SidebarHeader>
    <SidebarContent>
      <NavMain :menuConfig="menuItems" />
    </SidebarContent>
    <SidebarFooter>
      <NavUser />
    </SidebarFooter>
    <SidebarRail />
  </Sidebar>
</template>
