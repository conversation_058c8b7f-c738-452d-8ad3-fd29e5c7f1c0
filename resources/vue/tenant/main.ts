import '@/assets/app.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'

import App from './App.vue'
import router from './router'
import { useGlobalSettingsStore } from '@tenant/stores'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// preload app settings before mount
const settingsStore = useGlobalSettingsStore()
await settingsStore.loadSettings()

// Hide the loader with fade animation
const loader = document.getElementById('tenant-loader')
if (loader) {
  loader.classList.add('fade-out')
  setTimeout(() => {
    loader.remove()
  }, 300)
}

app.mount('#tenant-app')
