{"sections": [{"id": "branding", "name": "Settings", "purpose": "Customize your application's appearance by updating the app name, logo, and favicon to match your brand identity.", "instructions": {"add": "N/A", "edit": "Update your branding elements through the Settings page.", "remove": "N/A", "steps": [{"title": "How to change the app name", "description": "Update the name that appears in the browser tab and throughout the application.", "items": ["Navigate to Settings > Branding", "Locate the \"App Name\" field", "Enter your new app name (max 50 characters)", "Click \"Save Changes\" to apply your updates", "The changes will be reflected immediately across the application"]}, {"title": "How to change the app logo", "description": "Upload a new logo that will be displayed in the application header.", "items": ["Navigate to Settings > Branding", "Click on the logo upload area or the \"Upload\" button", "Select an image file (JPG or PNG, max 3MB)", "The recommended size is 200x50 pixels for optimal display", "The new logo will be saved automatically", "Refresh the page to see the updated logo"]}, {"title": "How to change the favicon", "description": "Update the small icon that appears in browser tabs.", "items": ["Navigate to Settings > Branding", "Click on the favicon upload area or the \"Upload\" button", "Select an image file (ICO or PNG, max 3MB)", "The recommended size is 32x32 pixels for best results", "The new favicon will be saved automatically", "Refresh the page to see the updated favicon in your browser tab"]}]}, "fields": [{"field_name": "App Name", "data_type": "text", "is_required": true, "description": "The name of your application that appears throughout the application.", "validation_rules": "Required field. Maximum 50 characters."}, {"field_name": "App Logo", "data_type": "image", "is_required": true, "description": "Your organization's logo displayed in the application header.", "validation_rules": "Recommended size: 200x50px. Formats: JPG, PNG. Max size: 3MB."}, {"field_name": "<PERSON><PERSON>", "data_type": "image", "is_required": true, "description": "The small icon that appears in browser tabs next to the page title.", "validation_rules": "Recommended size: 32x32px. Formats: ICO, PNG. Max size: 3MB."}]}, {"id": "categories", "name": "Categories & Sub-categories", "purpose": "Organize your products into categories and sub-categories to improve navigation and user experience. Categories help customers find products easily and are essential for organizing your inventory.", "instructions": {"add": "Add new categories and sub-categories to organize your products.", "edit": "Update existing categories and sub-categories to keep your product organization current.", "remove": "Remove categories and sub-categories that are no longer needed.", "steps": [{"title": "What is a category and why is it required?", "description": "A category is used to group and manage products. It is required for organizing products into logical sections.", "items": []}, {"title": "Where are categories shown and needed?", "description": "Categories are managed in the admin section under 'Categories'. They are used to generate public-facing pages for treatments, accessible via a URL like `/treatments/[category-slug]`.", "items": []}, {"title": "How do I add a new category?", "description": "On the 'Categories' page, click the 'Add Category' button. This will open a form drawer where you can enter the new category's details.", "items": []}, {"title": "How do I update an existing category?", "description": "In the category list, click the 'Edit' icon (pencil) in the 'Actions' column for the desired category. This will open the form drawer with the existing data, allowing you to make changes.", "items": []}, {"title": "How do I delete a category?", "description": "In the category list, click the 'Delete' icon (trash can) in the 'Actions' column. A confirmation dialog will appear to prevent accidental deletion. Confirming this action will permanently delete the category.", "items": []}, {"title": "How do I enable/disable a category?", "description": "In the category list, there is a toggle switch under the 'Status' column for each category. Clicking this switch will change the category's status between 'Active' and 'Inactive'.", "items": []}, {"title": "What is the purpose of the copy link under slug on the category list?", "description": "This button copies the direct public URL (`/treatments/[category-slug]`) for the category page to your clipboard. This is useful for sharing the link or using it in other marketing materials.", "items": []}, {"title": "What is a sub-category and why is it required?", "description": "A sub-category is a child of a main category. It is required for further, more specific organization of products within a broader category.", "items": []}, {"title": "Where are sub-categories shown and needed?", "description": "Sub-categories are managed in the admin section under 'Sub Categories'. They are used to further classify products under a main category.", "items": []}, {"title": "How do I add a new sub-category?", "description": "On the 'Sub Categories' page, click the 'Add Sub Category' button. This will open a form drawer where you can enter the new sub-category's details.", "items": []}, {"title": "How do I update an existing sub-category?", "description": "In the sub-category list, click the 'Edit' icon (pencil) in the 'Actions' column. This will open the form drawer with the existing data, allowing you to make changes.", "items": []}, {"title": "How do I delete a sub-category?", "description": "In the sub-category list, click the 'Delete' icon (trash can) in the 'Actions' column. A confirmation dialog will appear. Confirming this action will permanently delete the sub-category.", "items": []}, {"title": "How do I enable/disable a sub-category?", "description": "In the sub-category list, there is a toggle switch under the 'Status' column for each sub-category. Clicking this switch will change its status between 'Active' and 'Inactive'.", "items": []}]}, "category_fields": [{"field_name": "Category Name", "data_type": "string", "is_required": true, "description": "The name of the category.", "validation_rules": "Required."}, {"field_name": "Category Image", "data_type": "image", "is_required": true, "description": "An image representing the category.", "validation_rules": "Required."}, {"field_name": "Category Description", "data_type": "text", "is_required": false, "description": "A short description for the category.", "validation_rules": "Optional, max 100 characters."}, {"field_name": "Limit to Gender", "data_type": "string", "is_required": false, "description": "Limit the category to a specific gender.", "validation_rules": "Optional. Can be 'Male' or 'Female'."}], "subcategory_fields": [{"field_name": "Main Category", "data_type": "string", "is_required": true, "description": "The parent category for this sub-category.", "validation_rules": "Required."}, {"field_name": "Sub Category Name", "data_type": "string", "is_required": true, "description": "The name of the sub-category.", "validation_rules": "Required."}]}, {"id": "product", "name": "Product Management", "purpose": "This module is for adding, updating, and managing all products in your inventory. You can define product details, pricing, and categorize them for easy browsing.", "instructions": {"steps": [{"title": "How to Add a New Product", "description": "To add a new product, navigate to the 'Products' page from the sidebar and click the '+ Add Product' button.", "items": []}, {"title": "Understanding Product Types", "description": "When adding a product, you must first choose a 'Product Type'. The fields on the form will change based on your selection.", "items": ["Single Product: Best for individual medications, supplements, or standalone products. It has one set of details and one price.", "Program (Multiple Products): Best for treatment packages or bundles that include several different products. You can add multiple items to a program, each with its own details."]}, {"title": "How to Edit a Product", "description": "To edit a product, click the 'View' (eye) icon on the product list. This will take you to the product overview page where you can find an 'Edit' button.", "items": []}, {"title": "How to Delete a Product", "description": "Click the 'Delete' (trash can) icon in the 'Actions' column of the product list. A confirmation dialog will appear to prevent accidental deletion.", "items": []}, {"title": "How to Enable/Disable a Product", "description": "Use the toggle switch in the 'Status' column on the product list to change a product's status between 'Active' and 'Inactive'. Inactive products will not be visible on the public site.", "items": []}]}, "fields": [{"field_name": "Product Type", "data_type": "text", "is_required": true, "description": "Choose 'single' for a standard product or 'programs' for a product bundle. This choice determines which fields are available.", "validation_rules": "Required."}, {"field_name": "Main Category", "data_type": "text", "is_required": true, "description": "The main category the product belongs to.", "validation_rules": "Required."}, {"field_name": "Sub Category", "data_type": "text", "is_required": false, "description": "The sub-category the product belongs to. You must select a main category first.", "validation_rules": "Optional."}, {"field_name": "Product Image", "data_type": "image", "is_required": true, "description": "The product image.", "validation_rules": "Required. Must be JPG, JPEG, or PNG. Max size: 3MB."}, {"field_name": "Product Name", "data_type": "text", "is_required": true, "description": "The name of the product. (For 'single' product type)", "validation_rules": "Required for 'single' products."}, {"field_name": "Price", "data_type": "number", "is_required": true, "description": "The price of the product. (For 'single' product type)", "validation_rules": "Required for 'single' products."}, {"field_name": "Xpedicare URL", "data_type": "text", "is_required": true, "description": "The URL for the product on Xpedicare. (For 'single' product type)", "validation_rules": "Required for 'single' products. Must be a valid URL."}, {"field_name": "Product Title", "data_type": "text", "is_required": true, "description": "The title of the program. (For 'programs' product type)", "validation_rules": "Required for 'programs' products."}, {"field_name": "Product Items", "data_type": "array", "is_required": true, "description": "A list of the individual products included in the program. (For 'programs' product type)", "validation_rules": "Required for 'programs' products. Each item has its own name, price, etc."}, {"field_name": "Product Qty", "data_type": "text", "is_required": false, "description": "The quantity of the product (e.g., '30 tablets').", "validation_rules": "Optional."}, {"field_name": "Product Form", "data_type": "text", "is_required": false, "description": "The form of the product (e.g., 'tablet', 'capsule').", "validation_rules": "Optional."}, {"field_name": "Product Strength", "data_type": "text", "is_required": false, "description": "The strength of the product (e.g., '500mg').", "validation_rules": "Optional."}]}, {"id": "top-products", "name": "Top Products Management", "purpose": "This module allows you to select and manage up to 6 products that are featured on the homepage.", "instructions": {"steps": [{"title": "How to Add a Top Product", "description": "Navigate to 'Products' > 'Top Products'. Click the '+ Add Top Product' button. This opens a dialog where you can search and filter all available products. Click the 'Add' button next to a product to feature it.", "items": ["You can only add products that are not already in the top products list.", "There is a maximum of 6 top products."]}, {"title": "How to Remove a Top Product", "description": "In the 'Current Top Products' list, click the 'Remove' button next to the product you want to unfeature.", "items": []}, {"title": "How to Reorder Top Products", "description": "The order of products on the homepage is determined by their order in the 'Current Top Products' list. To reorder, click and hold the drag handle (grid icon) on the left of a product and drag it to your desired position. The new order is saved automatically.", "items": []}]}, "fields": []}, {"id": "landing-page", "name": "Landing Page Content", "purpose": "This module provides a live, on-page editor to modify the content of the main public-facing landing page. You can edit text, images, links, and more directly on the page for a real-time preview of your changes.", "instructions": {"steps": [{"title": "How to Start Editing the Landing Page", "description": "To enable the live editor, you must be logged in as an administrator.", "items": ["Log in to your admin account.", "Navigate to the main homepage of the website.", "A floating 'Edit Page' button will appear at the bottom-right corner.", "Click this button. The page will open in a new tab with the URL '/edit-main-site', and editing controls will become visible."]}, {"title": "How to Edit a Section", "description": "Once in edit mode, most sections on the page will have visible controls for editing.", "items": ["Hover over the section you wish to change (e.g., the main hero text, the features list, or the FAQ section).", "An 'Edit' icon or button will appear near the content.", "Click the edit icon. A dialog or modal will open with fields for the content of that specific section.", "Make your desired changes to the text, images, links, etc., and save the changes within the dialog."]}, {"title": "How to Save All Changes", "description": "After you have finished editing one or more sections, you must save your work to make it public.", "items": ["As soon as you make a change, a floating 'Save Changes' button will appear at the bottom-right of the screen.", "Click this button to save all the modifications you've made across the entire page.", "The button will show a 'Saving...' status and then disappear, confirming that your changes are live."]}]}, "fields": []}]}