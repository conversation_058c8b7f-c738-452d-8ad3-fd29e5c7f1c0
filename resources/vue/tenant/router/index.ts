import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '@tenant/layouts/MainLayout.vue'
import { defineMiddleware } from '@tenant/middleware'
import { auth, guest, validateOnboardingLink } from '@tenant/middleware/auth'
import { fetchCategories, fetchHeaderData } from '@tenant/middleware/landing'
import { useGlobalSettingsStore } from '@tenant/stores'

const routes = [
  // user routes
  {
    path: '/',
    name: 'Home',
    component: () => import('@tenant/views/user/LandingPage.vue'),
    beforeEnter: defineMiddleware([fetchCategories, fetchHeaderData]),
    meta: {
      title: 'Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/edit-main-site',
    name: 'EditMainSite',
    component: () => import('@tenant/views/user/LandingPage.vue'),
    beforeEnter: defineMiddleware([auth, fetchCategories, fetchHeaderData]),
    meta: {
      title: 'Editing Main Site',
    },
  },
  {
    path: '/treatments/:category_slug?/:sub_category_slug?',
    name: 'Treatments',
    component: () => import('@tenant/views/user/Treatments.vue'),
    beforeEnter: defineMiddleware(fetchHeaderData),
    meta: {
      title: 'Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },
  {
    path: '/treatment/:slug',
    name: 'TreatmentOverview',
    component: () => import('@tenant/views/user/TreatmentOverview.vue'),
    beforeEnter: defineMiddleware(fetchHeaderData),
    meta: {
      title: 'Personalized, High-Quality Treatments Shipped Directly to Your Doorstep',
    },
  },

  // Admin Onboarding
  {
    path: '/admin/onboard/create-password/:token',
    name: 'admin-onboard-create-password',
    component: () => import('@tenant/views/admin/onboarding/CreatePassword.vue'),
    beforeEnter: defineMiddleware(validateOnboardingLink),
    meta: {
      title: 'Create New Password',
    },
  },
  {
    path: '/admin/onboard/configure-pharmacy',
    name: 'admin-onboard-configure-app',
    component: () => import('@tenant/views/admin/onboarding/ConfigureApp.vue'),
    beforeEnter: defineMiddleware(auth),
    meta: {
      title: 'Configure Your Pharmacy',
    },
  },
  {
    path: '/link-expired',
    name: 'admin-onboard-link-expired',
    component: () => import('@tenant/views/admin/error/LinkExpired.vue'),
    meta: {
      title: 'Link Expired',
    },
  },

  // Admin Login
  {
    path: '/admin/login',
    name: 'admin-login',
    component: () => import('@tenant/views/admin/auth/LoginPage.vue'),
    beforeEnter: defineMiddleware(guest),
    meta: {
      title: 'Admin Login',
    },
  },

  // Admin Main Layout (Authenticated only)
  {
    path: '/admin',
    redirect: '/admin/dashboard',
    component: MainLayout,
    beforeEnter: defineMiddleware(auth),
    children: [
      // Dashboard
      {
        path: '/admin/dashboard',
        name: 'admin-dashboard',
        component: () => import('@tenant/views/admin/dashboard/DashboardPage.vue'),
        meta: {
          title: 'Dashboard',
        },
      },

      // Categories Management
      {
        path: '/admin/categories',
        name: 'admin-categories',
        component: () => import('@tenant/views/admin/category/CategoryListPage.vue'),
        meta: {
          title: 'Categories',
        },
      },

      // Sub-Categories Management
      {
        path: '/admin/sub-categories',
        name: 'admin-sub-categories',
        component: () => import('@tenant/views/admin/category/SubCategoryListPage.vue'),
        meta: {
          title: 'Sub-Categories',
        },
      },

      // Product Management
      {
        path: '/admin/products',
        name: 'admin-products',
        component: () => import('@tenant/views/admin/product/ProductListPage.vue'),
        meta: {
          title: 'Products',
        },
      },
      {
        path: '/admin/top-products',
        name: 'admin-top-products',
        component: () => import('@tenant/views/admin/product/TopProductsPage.vue'),
        meta: {
          title: 'Top Products',
        },
      },
      {
        path: '/admin/products/add',
        name: 'admin-products-add',
        component: () => import('@tenant/views/admin/product/ProductAddPage.vue'),
        meta: {
          title: 'Add Product',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },
      {
        path: '/admin/products/edit/:id',
        name: 'admin-products-edit',
        component: () => import('@tenant/views/admin/product/ProductEditPage.vue'),
        meta: {
          title: 'Edit Product',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },
      {
        path: '/admin/products/view/:id',
        name: 'admin-products-view',
        component: () => import('@tenant/views/admin/product/ProductViewPage.vue'),
        meta: {
          title: 'View Product',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },
      {
        path: '/admin/products/overview/:id',
        name: 'admin-products-overview',
        component: () => import('@tenant/views/admin/product/ProductOverviewPage.vue'),
        meta: {
          title: 'Product Overview',
          parent: 'Products',
          parentRouteName: 'admin-products',
        },
      },

      // Account Settings
      {
        path: '/admin/account',
        name: 'admin-account',
        component: () => import('@tenant/views/admin/account/AccountPage.vue'),
        meta: {
          title: 'Account Settings',
        },
      },

      // Application Settings
      {
        path: '/admin/settings',
        name: 'admin-settings',
        component: () => import('@tenant/views/admin/settings/SettingsPage.vue'),
        meta: {
          title: 'Settings',
        },
      },

      // Guide Page
      {
        path: '/admin/guide',
        name: 'admin-guide',
        component: () => import('@tenant/views/admin/guide/GuidePage.vue'),
        meta: {
          title: 'How to Guide',
        },
      },
    ],
  },

  // 404 Error Page (Catch-all route)
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@tenant/views/admin/error/Error404Page.vue'),
    meta: {
      title: '404 Not Found',
    },
  },
]

const router = createRouter({
  history: createWebHistory('/'),
  routes: routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    } else if (from.path === to.path) {
      return { top: 0 }
    } else {
      return { top: 0 }
    }
  },
})

router.beforeEach((to, _, next) => {
  const settingsStore = useGlobalSettingsStore()

  if (settingsStore.settings?.app_name) {
    document.title =
      `${to.meta.title} - ${settingsStore.settings?.app_name}` || settingsStore.settings?.app_name
  } else {
    document.title = to.meta.title as string
  }

  next()
})

export default router
