<script setup lang="ts">
import { computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useForm, Field } from 'vee-validate'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { RichTextEditor } from '@/components/ui/rich-text-editor'
import { ArrowLeft, Plus, Trash2, Save, Loader2 } from 'lucide-vue-next'
import { useProductStore, productOverviewSchema } from '@tenant/stores'
import { storeToRefs } from 'pinia'
import { toast } from 'vue-sonner'
import { toTypedSchema } from '@vee-validate/zod'
import type { z } from 'zod'

type ProductOverviewPayload = z.infer<typeof productOverviewSchema>

const productStore = useProductStore()
const { selectedProduct, isLoadingDetails, isUpdatingOverview, error, fieldErrors } =
  storeToRefs(productStore)
const { getProductById, updateProductOverview } = productStore

const router = useRouter()
const route = useRoute()
const productId = computed(() => route.params.id as string)

const formSchema = toTypedSchema(productOverviewSchema)

const form = useForm<ProductOverviewPayload>({
  validationSchema: formSchema,
  initialValues: {
    product_descriptions: selectedProduct.value?.product_descriptions || [],
    product_faqs: selectedProduct.value?.product_faqs || [],
  },
})

const { values, setFieldValue } = form

function addDescription() {
  const currentDescriptions = values.product_descriptions || []
  setFieldValue('product_descriptions', [...currentDescriptions, { title: '', description: '' }])
}

function removeDescription(index: number) {
  const currentDescriptions = values.product_descriptions || []
  const newDescriptions = currentDescriptions.filter((_, i) => i !== index)
  setFieldValue('product_descriptions', newDescriptions)
}

function addFAQ() {
  const currentFAQs = values.product_faqs || []
  setFieldValue('product_faqs', [...currentFAQs, { question: '', answer: '' }])
}

function removeFAQ(index: number) {
  const currentFAQs = values.product_faqs || []
  const newFAQs = currentFAQs.filter((_, i) => i !== index)
  setFieldValue('product_faqs', newFAQs)
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isUpdatingOverview.value) return

  const result = await updateProductOverview({
    ...values,
    id: productId.value,
  })

  if (result) {
    router.back()
  } else if (fieldErrors.value && Object.keys(fieldErrors.value).length > 0) {
    Object.entries(fieldErrors.value).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as any, messages[0])
      }
    })
  } else if (error.value) {
    toast.error(error.value)
  }
})

async function loadProduct() {
  if (productId.value) {
    await getProductById(productId.value)
    if (
      selectedProduct.value &&
      (!selectedProduct.value.product_descriptions || !selectedProduct.value.product_faqs)
    ) {
      if (!selectedProduct.value.product_descriptions) {
        selectedProduct.value.product_descriptions = []
      }
      if (!selectedProduct.value.product_faqs) {
        selectedProduct.value.product_faqs = []
      }
    }
  }
}

watch(selectedProduct, (newDetails) => {
  if (newDetails) {
    form.setValues({
      product_descriptions: newDetails.product_descriptions || [],
      product_faqs: newDetails.product_faqs || [],
    })
  }
})

onMounted(async () => {
  await loadProduct()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-3">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 class="text-2xl font-bold">Product Overview</h1>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoadingDetails || isLoadingDetails" class="flex items-center justify-center py-12">
      <Loader2 class="h-8 w-8 animate-spin" />
      <span class="ml-2">Loading product...</span>
    </div>

    <!-- Error State -->
    <Card v-else-if="error" class="border-destructive">
      <CardContent class="pt-6">
        <div class="text-center text-destructive">
          <p>{{ error }}</p>
        </div>
      </CardContent>
    </Card>

    <!-- Product Overview Form -->
    <div v-else-if="selectedProduct" class="space-y-8">
      <!-- Product Info -->
      <Card>
        <CardHeader>
          <CardTitle>Product Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="flex items-center gap-4">
            <div class="w-16 h-16 rounded-lg overflow-hidden bg-muted flex-shrink-0">
              <img
                v-if="selectedProduct.image"
                :src="selectedProduct.image"
                :alt="selectedProduct.product_title || 'Product'"
                class="w-full h-full object-cover"
              />
              <div
                v-else
                class="w-full h-full flex items-center justify-center text-muted-foreground text-xs"
              >
                No Image
              </div>
            </div>
            <div>
              <h3 class="font-semibold">
                {{
                  selectedProduct.product_type === 'programs' && selectedProduct.product_title
                    ? selectedProduct.product_title
                    : selectedProduct.product_items[0]?.product_name
                }}
              </h3>
              <p class="text-sm text-muted-foreground">{{ selectedProduct.slug }}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      <form @submit="onSubmit" class="space-y-8">
        <!-- Product Descriptions -->
        <Card>
          <CardHeader>
            <CardTitle>Product Descriptions</CardTitle>
          </CardHeader>
          <CardContent class="space-y-6">
            <div
              v-for="(_, index) in values.product_descriptions"
              :key="index"
              class="border rounded-lg p-6 space-y-4"
            >
              <div class="flex items-center justify-between">
                <h4 class="font-medium text-lg">Section {{ index + 1 }}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeDescription(index)"
                  class="text-destructive hover:text-destructive"
                >
                  <Trash2 class="h-4 w-4" />
                </Button>
              </div>
              <div class="space-y-4">
                <div>
                  <Label :for="`desc-title-${index}`" class="text-sm font-medium"
                    >Section Title</Label
                  >
                  <Field
                    :name="`product_descriptions.${index}.title`"
                    v-slot="{ componentField, errorMessage }"
                  >
                    <Input
                      :id="`desc-title-${index}`"
                      v-bind="componentField"
                      placeholder="Enter section title"
                      class="mt-1"
                      :class="{ 'border-destructive': errorMessage }"
                    />
                    <p v-if="errorMessage" class="text-sm text-destructive mt-1">
                      {{ errorMessage }}
                    </p>
                  </Field>
                </div>
                <div>
                  <Label :for="`desc-content-${index}`" class="text-sm font-medium"
                    >Section Description</Label
                  >
                  <Field
                    :name="`product_descriptions.${index}.description`"
                    v-slot="{ field, errorMessage }"
                  >
                    <div class="mt-1">
                      <RichTextEditor
                        :model-value="field.value"
                        @update:model-value="field.onChange"
                        placeholder="Enter section description with rich formatting..."
                        class="min-h-[150px]"
                      />
                      <p v-if="errorMessage" class="text-sm text-destructive mt-1">
                        {{ errorMessage }}
                      </p>
                    </div>
                  </Field>
                </div>
              </div>
            </div>

            <div
              v-if="!values.product_descriptions?.length"
              class="text-center py-12 text-muted-foreground"
            >
              <div class="space-y-2">
                <p class="text-lg">No description sections added yet</p>
                <p class="text-sm">Add your first section to get started</p>
              </div>
            </div>

            <!-- Add Section Button at Bottom -->
            <div class="flex justify-center pt-4">
              <Button type="button" variant="outline" @click="addDescription">
                <Plus class="h-4 w-4 mr-2" />
                Add Section
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- Product FAQs -->
        <Card>
          <CardHeader>
            <CardTitle>Product FAQs</CardTitle>
          </CardHeader>
          <CardContent class="space-y-6">
            <div
              v-for="(_, index) in values.product_faqs"
              :key="index"
              class="border rounded-lg p-6 space-y-4"
            >
              <div class="flex items-center justify-between">
                <h4 class="font-medium text-lg">FAQ {{ index + 1 }}</h4>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  @click="removeFAQ(index)"
                  class="text-destructive hover:text-destructive"
                >
                  <Trash2 class="h-4 w-4" />
                </Button>
              </div>
              <div class="space-y-4">
                <div>
                  <Label :for="`faq-question-${index}`" class="text-sm font-medium">Question</Label>
                  <Field
                    :name="`product_faqs.${index}.question`"
                    v-slot="{ componentField, errorMessage }"
                  >
                    <Input
                      :id="`faq-question-${index}`"
                      v-bind="componentField"
                      placeholder="Enter FAQ question"
                      class="mt-1"
                      :class="{ 'border-destructive': errorMessage }"
                    />
                    <p v-if="errorMessage" class="text-sm text-destructive mt-1">
                      {{ errorMessage }}
                    </p>
                  </Field>
                </div>
                <div>
                  <Label :for="`faq-answer-${index}`" class="text-sm font-medium">Answer</Label>
                  <Field
                    :name="`product_faqs.${index}.answer`"
                    v-slot="{ componentField, errorMessage }"
                  >
                    <Textarea
                      :id="`faq-answer-${index}`"
                      v-bind="componentField"
                      placeholder="Enter FAQ answer"
                      rows="4"
                      class="mt-1"
                      :class="{ 'border-destructive': errorMessage }"
                    />
                    <p v-if="errorMessage" class="text-sm text-destructive mt-1">
                      {{ errorMessage }}
                    </p>
                  </Field>
                </div>
              </div>
            </div>

            <div
              v-if="!values.product_faqs?.length"
              class="text-center py-12 text-muted-foreground"
            >
              <div class="space-y-2">
                <p class="text-lg">No FAQs added yet</p>
                <p class="text-sm">Add your first FAQ to get started</p>
              </div>
            </div>

            <!-- Add FAQ Button at Bottom -->
            <div class="flex justify-center pt-4">
              <Button type="button" variant="outline" @click="addFAQ">
                <Plus class="h-4 w-4 mr-2" />
                Add FAQ
              </Button>
            </div>
          </CardContent>
        </Card>

        <!-- Submit Button -->
        <div class="flex justify-end pt-6 border-t">
          <div class="flex gap-3">
            <Button type="button" variant="outline" @click="router.back()"> Cancel </Button>
            <Button type="submit" :disabled="isUpdatingOverview" class="min-w-[140px]">
              <Loader2 v-if="isUpdatingOverview" class="h-4 w-4 mr-2 animate-spin" />
              <Save v-else class="h-4 w-4 mr-2" />
              {{ isUpdatingOverview ? 'Saving...' : 'Save Overview' }}
            </Button>
          </div>
        </div>
      </form>
    </div>

    <!-- No Product Found -->
    <Card v-else>
      <CardContent class="pt-6">
        <div class="text-center text-muted-foreground">
          <p>Product not found.</p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
