<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useForm, FieldArray } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import {
  ArrowLeft,
  Plus,
  Trash2,
  Image,
  Loader2,
  Package,
  Layers,
  CheckCircle,
} from 'lucide-vue-next'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { productPayloadSchema, useProductStore, type ProductPayload } from '@tenant/stores'
import ConfirmationDialog from '@/components/ConfirmationDialog.vue'
import { storeToRefs } from 'pinia'
import { toast } from 'vue-sonner'
import { isEmptyObject, scrollToTop } from '@/lib'

const productStore = useProductStore()
const {
  selectedProduct,
  error: storeError,
  isUpdating,
  isUpdatingImg,
  isLoadingDetails,
  fieldErrors,
  categoryOptions,
  isLoadingCategoryOptions,
} = storeToRefs(productStore)
const { updateProduct, updateProductImg, getProductById, fetchCategoryOptions } = productStore

const router = useRouter()
const route = useRoute()
const formError = ref('')
const fileInputRef = ref<HTMLInputElement | null>(null)
const productId = computed(() => route.params.id as string)

// Product type change confirmation
const isTypeChangeDialogOpen = ref(false)
const pendingProductType = ref<'single' | 'programs' | null>(null)

const formSchema = toTypedSchema(productPayloadSchema)

const form = useForm<ProductPayload>({
  validationSchema: formSchema,
  initialValues: {
    product_type: 'single',
    main_category_id: '',
    sub_category_id: '',
    image: '',
    // Single product fields
    product_name: '',
    product_qty: '',
    product_form: '',
    product_strength: '',
    price: '',
    xpedicare_url: '',
    // Program product fields
    product_title: '',
    product_items: [],
  } as ProductPayload,
})

const selectedMainCategoryId = computed(() => form.values.main_category_id)
watch(selectedMainCategoryId, async (newCategoryId) => {
  if (newCategoryId) {
    form.setFieldValue('sub_category_id', '')
  }
})

const subCategoryOptions = computed(() => {
  const mainCategoryId = form.values.main_category_id
  return categoryOptions.value.find((c) => c.id === mainCategoryId)?.sub_categories || []
})

// Watch for product type changes to reset form
const productType = computed(() => form.values.product_type)
watch(productType, (newType) => {
  if (newType === 'single') {
    // form.setFieldValue('product_title', '')
    // form.setFieldValue('product_items', [])
  } else if (newType === 'programs') {
    // form.setFieldValue('product_name', '')
    // form.setFieldValue('product_qty', '')
    // form.setFieldValue('product_form', '')
    // form.setFieldValue('product_strength', '')
    // form.setFieldValue('price', '')
    // form.setFieldValue('xpedicare_url', '')
    // Add a default item
    // form.setFieldValue('product_items', [
    //   {
    //     product_name: '',
    //     product_qty: '',
    //     product_form: '',
    //     product_strength: '',
    //     price: '',
    //     xpedicare_url: '',
    //   },
    // ])
  }
})

function triggerImagePicker() {
  fileInputRef.value?.click()
}

async function handleImageChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return

  // Validate file type (only JPG/JPEG/PNG)
  // const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png']
  // if (!allowedTypes.includes(file.type.toLowerCase())) {
  //   toast.error('Please select a JPG, JPEG or PNG image file')
  //   // Reset input
  //   if (fileInputRef.value) fileInputRef.value.value = ''
  //   return
  // }

  if (file.size > 3 * 1024 * 1024) {
    toast.error('Please upload a product image smaller than 3MB.')
    // Reset input
    if (fileInputRef.value) fileInputRef.value.value = ''
    return
  }

  try {
    const reader = new FileReader()
    reader.onload = async () => {
      const base64 = reader.result as string

      // Update form field immediately for preview
      form.setFieldValue('image', base64)
      form.validateField('image')

      if (productId.value) {
        const result = await updateProductImg({
          id: productId.value,
          image: base64,
        })

        if (result) {
          toast.success('Product image updated successfully')
          // await loadProductData()
        } else {
          toast.error('Failed to upload image. Please try again.')
        }
      } else {
        toast.error('Cannot upload image: Product not saved yet')
      }

      // Reset input
      if (fileInputRef.value) fileInputRef.value.value = ''
    }

    reader.onerror = () => {
      toast.error('Failed to read image file')
    }

    reader.readAsDataURL(file)
  } catch (error) {
    toast.error('An error occurred while uploading the image')
    console.error('Image upload error:', error)
  }
}

function addProgramItem() {
  const currentValues = form.values as any
  const currentItems = currentValues.product_items || []
  form.setFieldValue('product_items', [
    ...currentItems,
    {
      product_name: '',
      product_qty: '',
      product_form: '',
      product_strength: '',
      price: '',
      xpedicare_url: '',
    },
  ])
}

// Handle product type change with confirmation
function handleProductTypeChange(newType: 'single' | 'programs') {
  const currentType = form.values.product_type

  if (currentType === newType) return

  // Check if there's existing data that would be lost
  const hasData =
    currentType === 'single'
      ? form.values.product_name ||
        form.values.product_qty ||
        form.values.product_form ||
        form.values.product_strength ||
        form.values.price ||
        form.values.xpedicare_url
      : form.values.product_title ||
        (form.values.product_items && form.values.product_items.length > 0)

  if (hasData) {
    pendingProductType.value = newType
    isTypeChangeDialogOpen.value = true
  } else {
    confirmProductTypeChange(newType)
  }
}

function confirmProductTypeChange(newType?: 'single' | 'programs') {
  const typeToSet = newType || pendingProductType.value
  if (!typeToSet) return

  form.setFieldValue('product_type', typeToSet)

  // Clear type-specific fields
  if (typeToSet === 'single') {
    form.setFieldValue('product_title', '')
    form.setFieldValue('product_items', [])
  } else {
    form.setFieldValue('product_name', '')
    form.setFieldValue('product_qty', '')
    form.setFieldValue('product_form', '')
    form.setFieldValue('product_strength', '')
    form.setFieldValue('price', '')
    form.setFieldValue('xpedicare_url', '')
  }

  isTypeChangeDialogOpen.value = false
  pendingProductType.value = null
}

// Load product data and populate form
async function loadProductData() {
  if (!productId.value) return

  const result = await getProductById(productId.value)

  if (result && selectedProduct.value) {
    const product = selectedProduct.value

    // Set form values based on product type
    if (product.product_type === 'single' && product.product_items.length > 0) {
      const item = product.product_items[0]
      form.setValues({
        product_type: 'single',
        main_category_id: product.main_category_id,
        sub_category_id: product.sub_category_id || '',
        image: product.image,
        product_name: item.product_name,
        product_qty: item.product_qty || '',
        product_form: item.product_form || '',
        product_strength: item.product_strength || '',
        price: item.price.toString(),
        xpedicare_url: item.xpedicare_url,
      } as ProductPayload)
    } else if (product.product_type === 'programs') {
      form.setValues({
        product_type: 'programs',
        main_category_id: product.main_category_id,
        sub_category_id: product.sub_category_id || '',
        image: product.image,
        product_title: product.product_title || '',
        product_items: product.product_items.map((item) => ({
          product_name: item.product_name,
          product_qty: item.product_qty || '',
          product_form: item.product_form || '',
          product_strength: item.product_strength || '',
          price: item.price.toString(),
          xpedicare_url: item.xpedicare_url,
        })),
      } as ProductPayload)
    }
  }
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isUpdating.value) return
  formError.value = ''

  // if xpedicare_url does not starts with https add it
  if (values.product_type === 'single') {
    values = {
      ...values,
      xpedicare_url: values.xpedicare_url.startsWith('http')
        ? values.xpedicare_url
        : `https://${values.xpedicare_url}`,
    }
  } else {
    values = {
      ...values,
      product_items: values.product_items.map((item) => ({
        ...item,
        xpedicare_url: item.xpedicare_url.startsWith('http')
          ? item.xpedicare_url
          : `https://${item.xpedicare_url}`,
      })),
    }
  }

  const result = await updateProduct({ ...values, id: productId.value })

  if (result) {
    toast.success('Product updated successfully')
    router.back()
  } else if (!isEmptyObject(fieldErrors.value)) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof ProductPayload, messages[0])
      }
    })
  } else if (storeError.value) {
    formError.value = storeError.value
  } else {
    formError.value = 'Failed to update product. Please try again.'
  }

  scrollToTop()
})

onMounted(async () => {
  await Promise.all([fetchCategoryOptions(), loadProductData()])
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Edit Product</h1>
    </div>

    <!-- Loading State -->
    <div v-if="isLoadingDetails" class="flex items-center justify-center py-8">
      <Loader2 class="h-8 w-8 animate-spin" />
      <span class="ml-2">Loading product...</span>
    </div>

    <!-- Form -->
    <Card v-else>
      <CardContent class="pt-6">
        <form class="space-y-8" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Product Type Selection -->
          <div>
            <h2 class="text-lg font-medium mb-4">Product Type</h2>
            <FormField name="product_type">
              <FormItem>
                <FormControl>
                  <RadioGroup
                    :model-value="form.values.product_type"
                    @update:model-value="
                      (value) => handleProductTypeChange(value as ProductPayload['product_type'])
                    "
                    class="grid grid-cols-1 md:grid-cols-2 gap-4"
                  >
                    <!-- Single Product Option -->
                    <div class="relative">
                      <RadioGroupItem value="single" id="single-edit" class="peer sr-only" />
                      <label
                        for="single-edit"
                        class="flex flex-col items-start p-4 border-2 border-muted rounded-lg cursor-pointer transition-all hover:border-primary/50 relative"
                        :class="{
                          'border-primary bg-primary/5': form.values.product_type === 'single',
                        }"
                      >
                        <div class="flex items-center justify-between w-full mb-2">
                          <div class="flex items-center gap-3">
                            <Package class="h-5 w-5 text-primary" />
                            <span class="font-medium">Single Product</span>
                          </div>
                          <CheckCircle
                            v-if="form.values.product_type === 'single'"
                            class="h-5 w-5 text-primary"
                          />
                        </div>
                        <p class="text-sm text-muted-foreground text-left">
                          Create a single product with one set of details, pricing, and
                          specifications.
                        </p>
                        <div class="mt-2 text-xs text-muted-foreground">
                          Best for: Individual medications, supplements, or standalone products
                        </div>
                      </label>
                    </div>

                    <!-- Program Option -->
                    <div class="relative">
                      <RadioGroupItem value="programs" id="programs-edit" class="peer sr-only" />
                      <label
                        for="programs-edit"
                        class="flex flex-col items-start p-4 border-2 border-muted rounded-lg cursor-pointer transition-all hover:border-primary/50 relative"
                        :class="{
                          'border-primary bg-primary/5': form.values.product_type === 'programs',
                        }"
                      >
                        <div class="flex items-center justify-between w-full mb-2">
                          <div class="flex items-center gap-3">
                            <Layers class="h-5 w-5 text-primary" />
                            <span class="font-medium">Program (Multiple Products)</span>
                          </div>
                          <CheckCircle
                            v-if="form.values.product_type === 'programs'"
                            class="h-5 w-5 text-primary"
                          />
                        </div>
                        <p class="text-sm text-muted-foreground text-left">
                          Create a program containing multiple related products with different
                          specifications and pricing.
                        </p>
                        <div class="mt-2 text-xs text-muted-foreground">
                          Best for: Treatment packages, product bundles, or multi-step programs
                        </div>
                      </label>
                    </div>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <!-- Category Selection -->
          <!-- Basic Information Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Basic Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- Main Category -->
              <FormField name="main_category_id" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Main Category *</FormLabel>
                  <FormControl>
                    <Select
                      :model-value="field.value"
                      @update:model-value="field.onChange($event)"
                      :disabled="isLoadingCategoryOptions"
                    >
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectValue placeholder="Select main category" />
                      </SelectTrigger>
                      <SelectContent>
                        <div v-if="isLoadingCategoryOptions" class="p-2 text-center text-sm">
                          Loading categories...
                        </div>
                        <div
                          v-else-if="categoryOptions.length === 0"
                          class="p-2 text-center text-sm"
                        >
                          No categories available
                        </div>
                        <SelectItem
                          v-for="category in categoryOptions"
                          :key="category.id"
                          :value="category.id"
                        >
                          {{ category.name }}{{ category.gender ? ` (${category.gender})` : '' }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Sub Category -->
              <FormField name="sub_category_id" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Sub Category (optional)</FormLabel>
                  <FormControl>
                    <Select
                      :model-value="field.value"
                      @update:model-value="field.onChange($event)"
                      :disabled="!selectedMainCategoryId"
                    >
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectValue placeholder="Select sub category" />
                      </SelectTrigger>
                      <SelectContent>
                        <div v-if="!selectedMainCategoryId" class="p-2 text-center text-sm">
                          Please select a main category first
                        </div>
                        <div
                          v-else-if="subCategoryOptions.length === 0"
                          class="p-2 text-center text-sm"
                        >
                          No sub-categories available for
                          {{ categoryOptions.find((c) => c.id === selectedMainCategoryId)?.name }}
                        </div>
                        <SelectItem
                          v-for="subCategory in subCategoryOptions"
                          :key="subCategory.id"
                          :value="subCategory.id"
                        >
                          {{ subCategory.name }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Product Image Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Product Image</h2>
            <FormField name="image">
              <FormItem>
                <div class="flex items-center gap-4">
                  <!-- Image Preview -->
                  <div class="relative">
                    <img
                      v-if="form.values.image"
                      :src="form.values.image"
                      alt="Selected image preview"
                      class="h-20 w-20 rounded object-cover border"
                      :class="{ 'opacity-50': isUpdatingImg }"
                    />
                    <div v-else class="h-20 w-20 rounded border flex items-center justify-center">
                      <Image class="h-8 w-8 text-muted-foreground" />
                    </div>

                    <!-- Upload Progress Overlay -->
                    <div
                      v-if="isUpdatingImg"
                      class="absolute inset-0 flex items-center justify-center bg-black/20 rounded"
                    >
                      <Loader2 class="h-6 w-6 animate-spin text-white" />
                    </div>
                  </div>

                  <!-- Upload Button -->
                  <div class="flex flex-col gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      :disabled="isUpdating || isUpdatingImg"
                      @click="triggerImagePicker"
                    >
                      <Loader2 v-if="isUpdatingImg" class="h-4 w-4 animate-spin mr-2" />
                      {{ isUpdatingImg ? 'Uploading...' : 'Select Image' }}
                    </Button>

                    <!-- Upload Status -->
                    <div v-if="isUpdatingImg" class="text-xs text-muted-foreground">
                      Uploading image to server...
                    </div>
                  </div>

                  <input
                    ref="fileInputRef"
                    type="file"
                    accept="image/*"
                    class="hidden"
                    @change="handleImageChange"
                  />
                </div>

                <!-- Error Message -->
                <div v-if="storeError" class="mt-2 text-sm text-destructive">
                  {{ storeError }}
                </div>

                <!-- Help Text -->
                <div class="mt-2 text-xs text-muted-foreground">Maximum size allowed: 3MB</div>

                <FormMessage />
              </FormItem>
            </FormField>
          </div>

          <!-- Single Product Fields -->
          <div v-if="form.values.product_type === 'single'">
            <h2 class="text-lg font-medium mb-4">Product Details</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- Product Name -->
              <FormField name="product_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Name *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter product name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Product Quantity -->
              <FormField name="product_qty" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Quantity (optional)</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter quantity"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Product Form -->
              <FormField name="product_form" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Form (optional)</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter product form"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Product Strength -->
              <FormField name="product_strength" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Product Strength (optional)</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter product strength"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Price -->
              <FormField name="price" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Price *</FormLabel>
                  <FormControl>
                    <div class="relative w-full items-center">
                      <Input
                        v-bind="componentField"
                        placeholder="Enter price"
                        class="pl-8"
                        :aria-invalid="!!errorMessage"
                      />
                      <span
                        class="absolute start-1 inset-y-0 flex items-center justify-center px-2"
                      >
                        <span class="text-muted-foreground">$</span>
                      </span>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Xpedicare URL -->
              <FormField name="xpedicare_url" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Xpedicare URL *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter Xpedicare URL"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Program Product Fields -->
          <div v-if="form.values.product_type === 'programs'">
            <h2 class="text-lg font-medium mb-4">Program Details</h2>

            <!-- Program Title -->
            <div class="mb-6">
              <FormField name="product_title" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Program Title *</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter program title"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>

            <!-- Program Items -->
            <div>
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-md font-medium">Program Items</h3>
              </div>

              <FieldArray name="product_items" v-slot="{ fields, remove }">
                <div v-if="fields.length === 0" class="text-center py-8 text-muted-foreground">
                  No items added yet. Click "Add Item" to get started.
                </div>

                <div
                  v-for="(field, index) in fields"
                  :key="field.key"
                  class="border rounded-lg p-4 mb-4"
                >
                  <div class="flex items-center justify-between mb-4">
                    <h4 class="font-medium">Item {{ index + 1 }}</h4>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      @click="remove(index)"
                      class="text-destructive hover:text-destructive"
                    >
                      <Trash2 class="h-4 w-4" />
                    </Button>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Product Name -->
                    <FormField
                      :name="`product_items.${index}.product_name`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Name *</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter product name"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Product Quantity -->
                    <FormField
                      :name="`product_items.${index}.product_qty`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Quantity (optional)</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter quantity"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Product Form -->
                    <FormField
                      :name="`product_items.${index}.product_form`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Form (optional)</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter product form"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Product Strength -->
                    <FormField
                      :name="`product_items.${index}.product_strength`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Product Strength (optional)</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter product strength"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Price -->
                    <FormField
                      :name="`product_items.${index}.price`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Price *</FormLabel>
                        <FormControl>
                          <div class="relative w-full items-center">
                            <Input
                              v-bind="componentField"
                              placeholder="Enter price"
                              class="pl-8"
                              :aria-invalid="!!errorMessage"
                            />
                            <span
                              class="absolute start-1 inset-y-0 flex items-center justify-center px-2"
                            >
                              <span class="text-muted-foreground">$</span>
                            </span>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>

                    <!-- Xpedicare URL -->
                    <FormField
                      :name="`product_items.${index}.xpedicare_url`"
                      v-slot="{ componentField, errorMessage }"
                    >
                      <FormItem>
                        <FormLabel>Xpedicare URL *</FormLabel>
                        <FormControl>
                          <Input
                            v-bind="componentField"
                            placeholder="Enter Xpedicare URL"
                            :aria-invalid="!!errorMessage"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    </FormField>
                  </div>
                </div>

                <div class="flex justify-center">
                  <Button type="button" variant="outline" size="sm" @click="addProgramItem">
                    <Plus class="h-4 w-4 mr-2" />
                    Add Item
                  </Button>
                </div>
              </FieldArray>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end space-x-4">
            <Button type="button" variant="outline" @click="router.back()"> Cancel </Button>
            <Button type="submit" :disabled="isUpdating || isUpdatingImg">
              <Loader2 v-if="isUpdating || isUpdatingImg" class="h-4 w-4 animate-spin mr-2" />
              {{
                isUpdatingImg ? 'Uploading Image...' : isUpdating ? 'Updating...' : 'Update Product'
              }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- Product Type Change Confirmation Dialog -->
    <ConfirmationDialog
      v-model:open="isTypeChangeDialogOpen"
      title="Change Product Type?"
      description="Changing the product type will clear all existing product-specific data. This action cannot be undone. Are you sure you want to continue?"
      confirmText="Change Type"
      @confirm="confirmProductTypeChange()"
      @cancel="pendingProductType = null"
    />
  </div>
</template>
