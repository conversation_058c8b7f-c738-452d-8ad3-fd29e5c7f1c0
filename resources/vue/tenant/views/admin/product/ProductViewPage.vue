<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Badge } from '@/components/ui/badge'
import { ArrowLeft, Loader2, FileText, EditIcon, ChevronDown } from 'lucide-vue-next'
import { useProductStore } from '@tenant/stores'
import { storeToRefs } from 'pinia'

const productStore = useProductStore()
const { selectedProduct, isLoadingDetails, error } = storeToRefs(productStore)
const { getProductById } = productStore

const router = useRouter()
const route = useRoute()
const productId = computed(() => route.params.id as string)

function navigateToEdit() {
  router.push({ name: 'admin-products-edit', params: { id: productId.value } })
}

function navigateToOverview() {
  router.push({ name: 'admin-products-overview', params: { id: productId.value } })
}

async function loadProduct() {
  if (productId.value) {
    await getProductById(productId.value)
  }
}

onMounted(() => {
  loadProduct()
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center justify-between mb-6">
      <div class="flex items-center gap-3">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
        <h1 class="text-2xl font-bold">Product Details</h1>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoadingDetails" class="flex items-center justify-center py-12">
      <Loader2 class="h-8 w-8 animate-spin" />
      <span class="ml-2">Loading product...</span>
    </div>

    <!-- Error State -->
    <Card v-else-if="error" class="border-destructive">
      <CardContent class="pt-6">
        <div class="text-center text-destructive">
          <p>{{ error }}</p>
        </div>
      </CardContent>
    </Card>

    <!-- Product Details -->
    <div v-else-if="selectedProduct" class="space-y-6">
      <!-- Basic Information -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Basic Information</CardTitle>
            <Button @click="navigateToEdit" size="sm">
              <EditIcon class="h-4 w-4 mr-2" />
              Edit <span class="hidden sm:inline">Details</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="flex flex-col sm:flex-row items-start gap-6">
            <div class="w-32 h-32 rounded-lg overflow-hidden bg-muted flex-shrink-0">
              <img
                v-if="selectedProduct.image"
                :src="selectedProduct.image"
                :alt="selectedProduct.product_title || 'Product'"
                class="w-full h-full object-cover"
              />
              <div
                v-else
                class="w-full h-full flex items-center justify-center text-muted-foreground"
              >
                No Image
              </div>
            </div>
            <div class="flex-1 space-y-3">
              <div>
                <h2 class="text-xl font-semibold">
                  {{
                    selectedProduct.product_type === 'programs' && selectedProduct.product_title
                      ? selectedProduct.product_title
                      : selectedProduct.product_items[0]?.product_name
                  }}
                </h2>
                <p class="text-muted-foreground">{{ selectedProduct.slug }}</p>
              </div>
              <div class="flex items-center gap-4">
                <Badge
                  :variant="selectedProduct.product_type === 'single' ? 'default' : 'secondary'"
                >
                  {{ selectedProduct.product_type === 'single' ? 'Single Product' : 'Program' }}
                </Badge>
                <Badge :variant="selectedProduct.is_active ? 'default' : 'secondary'">
                  {{ selectedProduct.is_active ? 'Active' : 'Inactive' }}
                </Badge>
              </div>
              <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span class="font-medium">Main Category:</span>
                  <p>{{ selectedProduct.main_category_name }}</p>
                </div>
                <div v-if="selectedProduct.sub_category_name">
                  <span class="font-medium">Sub Category:</span>
                  <p>{{ selectedProduct.sub_category_name }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Product Items -->
          <div class="mt-8">
            <div class="mb-4">
              <h4 class="font-semibold">
                {{
                  selectedProduct.product_type === 'single' ? 'Product Details' : 'Program Items'
                }}
              </h4>
            </div>
            <div>
              <div class="space-y-6">
                <div
                  v-for="(item, index) in selectedProduct.product_items"
                  :key="item.id"
                  class="border rounded-lg p-4"
                >
                  <div class="flex items-center justify-between mb-4">
                    <h3 class="font-medium">
                      {{
                        selectedProduct.product_type === 'single' ? 'Product' : `Item ${index + 1}`
                      }}
                    </h3>
                    <Badge variant="outline">${{ item.price.toFixed(2) }}</Badge>
                  </div>

                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                    <div>
                      <span class="text-sm font-medium text-muted-foreground">Name:</span>
                      <p>{{ item.product_name ?? '-' }}</p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-muted-foreground">Form:</span>
                      <p>{{ item.product_form ?? '-' }}</p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-muted-foreground">Strength:</span>
                      <p>{{ item.product_strength ?? '-' }}</p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-muted-foreground">Quantity:</span>
                      <p>{{ item.product_qty ?? '-' }}</p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-muted-foreground">Price:</span>
                      <p>${{ item.price.toFixed(2) }}</p>
                    </div>
                    <div>
                      <span class="text-sm font-medium text-muted-foreground">Xpedicare URL:</span>
                      <a
                        :href="item.xpedicare_url"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="text-primary hover:underline flex items-center gap-1"
                      >
                        <span class="break-words text-wrap w-full">{{ item.xpedicare_url }}</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Product Overview -->
      <Card>
        <CardHeader>
          <div class="flex items-center justify-between">
            <CardTitle>Product Overview</CardTitle>
            <Button @click="navigateToOverview" size="sm">
              <EditIcon class="h-4 w-4 mr-2" />
              Edit <span class="hidden sm:inline">Overview</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent class="space-y-6">
          <!-- Product Descriptions -->
          <div v-if="selectedProduct.product_descriptions?.length">
            <h3 class="text-lg font-semibold mb-4">Product Descriptions</h3>
            <div class="space-y-3">
              <Collapsible
                v-for="(description, index) in selectedProduct.product_descriptions"
                :key="index"
                class="border rounded-lg"
              >
                <CollapsibleTrigger
                  class="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
                >
                  <h4 class="font-medium">{{ description.title }}</h4>
                  <ChevronDown
                    class="h-4 w-4 transition-transform duration-200 [&[data-state=open]]:rotate-180"
                  />
                </CollapsibleTrigger>
                <CollapsibleContent class="p-4">
                  <div
                    class="prose prose-sm w-full text-muted-foreground break-words overflow-x-hidden"
                    v-html="description.description"
                  ></div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </div>

          <!-- Product FAQs -->
          <div v-if="selectedProduct.product_faqs?.length">
            <h3 class="text-lg font-semibold mb-4">Frequently Asked Questions</h3>
            <div class="space-y-3">
              <Collapsible
                v-for="(faq, index) in selectedProduct.product_faqs"
                :key="index"
                class="border rounded-lg"
              >
                <CollapsibleTrigger
                  class="flex w-full items-center justify-between p-4 text-left hover:bg-muted/50 transition-colors"
                >
                  <h4 class="font-medium">{{ faq.question }}</h4>
                  <ChevronDown
                    class="h-4 w-4 transition-transform duration-200 [&[data-state=open]]:rotate-180"
                  />
                </CollapsibleTrigger>
                <CollapsibleContent class="p-4">
                  <p class="text-muted-foreground break-words">{{ faq.answer }}</p>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </div>

          <!-- Empty State -->
          <div
            v-if="
              !selectedProduct.product_descriptions?.length && !selectedProduct.product_faqs?.length
            "
            class="text-center py-8 text-muted-foreground"
          >
            <FileText class="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p class="text-lg mb-2">No overview content available</p>
            <p class="text-sm mb-4">
              Add product descriptions and FAQs to provide detailed information about this product.
            </p>
            <Button @click="navigateToOverview" variant="outline">
              <FileText class="h-4 w-4 mr-2" />
              Add Overview Content
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- No Product Found -->
    <Card v-else>
      <CardContent class="pt-6">
        <div class="text-center text-muted-foreground">
          <p>Product not found.</p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
