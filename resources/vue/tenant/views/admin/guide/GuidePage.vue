<script setup lang="ts">
import { ChevronRight } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'
import guideData from '@tenant/data/guideData.json'

interface Field {
  field_name: string
  data_type: string
  is_required: boolean
  default_value?: string
  description: string
  validation_rules: string
}

interface Step {
  title: string
  description: string
  items: string[]
}

interface Instructions {
  add?: string
  edit?: string
  remove?: string
  steps?: Step[]
}

interface Module {
  id: string
  name: string
  purpose: string
  instructions: Instructions
  fields?: Field[]
  category_fields?: Field[]
  subcategory_fields?: Field[]
}

const modules: Module[] = guideData.sections
</script>

<template>
  <div class="space-y-6">
    <div id="top" class="flex flex-col space-y-4">
      <div>
        <h1 class="text-2xl font-bold tracking-tight">How to Guide</h1>
        <p class="text-muted-foreground text-sm">
          A comprehensive guide to managing your store's content, products, and settings through the
          admin panel.
        </p>
      </div>

      <!-- Table of Contents -->
      <Card class="mb-6 bg-accent">
        <CardHeader>
          <CardTitle>Table of Contents</CardTitle>
        </CardHeader>
        <CardContent>
          <nav>
            <ul class="space-y-2 list-disc pl-5">
              <li v-for="module in modules" :key="module.id">
                <a :href="`#${module.id}`" class="text-primary hover:underline flex items-center">
                  {{ module.name }}
                </a>
              </li>
            </ul>
          </nav>
        </CardContent>
      </Card>
    </div>
    <div
      v-for="moduleItem in modules"
      :key="moduleItem.id"
      :id="moduleItem.id"
      class="scroll-mt-20"
    >
      <Card class="mb-8 gap-2">
        <CardHeader>
          <!-- <div class="flex justify-between items-center"> -->
          <CardTitle class="text-2xl">{{ moduleItem.name }}</CardTitle>
          <CardDescription class="text-base">{{ moduleItem.purpose }}</CardDescription>
          <!-- </div> -->
        </CardHeader>
        <CardContent>
          <!-- <p class="text-muted-foreground mb-6">{{ moduleItem.purpose }}</p> -->

          <h3 class="text-lg font-semibold mt-6 mb-4">How to Use</h3>
          <div class="space-y-6 mb-6">
            <!-- Steps for modules with detailed steps -->
            <template
              v-if="
                ['branding', 'categories', 'product', 'top-products', 'landing-page'].includes(
                  moduleItem.id,
                ) && moduleItem.instructions.steps
              "
            >
              <div
                v-for="(step, stepIndex) in moduleItem.instructions.steps"
                :key="stepIndex"
                class="bg-muted/30 p-4 rounded-lg"
              >
                <h4 class="font-semibold text-base flex items-center gap-2 mb-2">
                  <span
                    class="flex items-center justify-center h-6 w-6 rounded-full bg-primary/10 text-primary text-sm"
                    >{{ stepIndex + 1 }}</span
                  >
                  {{ step.title }}
                </h4>
                <p class="text-muted-foreground text-sm mb-3">{{ step.description }}</p>
                <ul v-if="step.items.length > 0" class="list-disc pl-5 space-y-1.5 text-sm">
                  <li v-for="(item, i) in step.items" :key="i" class="text-muted-foreground">
                    {{ item }}
                  </li>
                </ul>
              </div>
            </template>

            <!-- Instructions for other modules -->
            <div v-else class="space-y-4">
              <div>
                <h4 class="font-medium mb-1">To add a new item:</h4>
                <p class="text-sm text-muted-foreground">{{ moduleItem.instructions.add }}</p>
              </div>
              <div>
                <h4 class="font-medium mb-1">To edit an existing item:</h4>
                <p class="text-sm text-muted-foreground">{{ moduleItem.instructions.edit }}</p>
              </div>
              <div>
                <h4 class="font-medium mb-1">To remove an item:</h4>
                <p class="text-sm text-muted-foreground">{{ moduleItem.instructions.remove }}</p>
              </div>
            </div>
          </div>

          <!-- Fields for Categories -->
          <template v-if="moduleItem.id === 'categories'">
            <div v-if="moduleItem.category_fields?.length ?? 0 > 0">
              <h3 class="text-lg font-semibold mb-4">Category Fields</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Field Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Required</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Validation Rules</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-for="field in moduleItem.category_fields" :key="field.field_name">
                    <TableCell class="font-mono">{{ field.field_name }}</TableCell>
                    <TableCell>{{ field.data_type }}</TableCell>
                    <TableCell>
                      <Badge :variant="field.is_required ? 'default' : 'outline'">
                        {{ field.is_required ? 'Yes' : 'No' }}
                      </Badge>
                    </TableCell>
                    <TableCell>{{ field.description }}</TableCell>
                    <TableCell>{{ field.validation_rules }}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
            <div v-if="moduleItem.subcategory_fields?.length ?? 0 > 0" class="mt-8">
              <h3 class="text-lg font-semibold mb-4">Sub-Category Fields</h3>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Field Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Required</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Validation Rules</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow v-for="field in moduleItem.subcategory_fields" :key="field.field_name">
                    <TableCell class="font-mono">{{ field.field_name }}</TableCell>
                    <TableCell>{{ field.data_type }}</TableCell>
                    <TableCell>
                      <Badge :variant="field.is_required ? 'default' : 'outline'">
                        {{ field.is_required ? 'Yes' : 'No' }}
                      </Badge>
                    </TableCell>
                    <TableCell>{{ field.description }}</TableCell>
                    <TableCell>{{ field.validation_rules }}</TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </template>

          <!-- Fields for other modules -->
          <div v-else-if="(moduleItem.fields?.length ?? 0) > 0">
            <h3 class="text-lg font-semibold mb-4">Available Fields</h3>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Field Name</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Required</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Validation Rules</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow v-for="field in moduleItem.fields" :key="field.field_name">
                  <TableCell class="font-mono">{{ field.field_name }}</TableCell>
                  <TableCell>{{ field.data_type }}</TableCell>
                  <TableCell>
                    <Badge :variant="field.is_required ? 'default' : 'outline'">
                      {{ field.is_required ? 'Yes' : 'No' }}
                    </Badge>
                  </TableCell>
                  <TableCell>{{ field.description }}</TableCell>
                  <TableCell>{{ field.validation_rules }}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>

          <!-- Back to Top Button -->
          <div class="mt-6 pt-4 border-t">
            <a
              href="#top"
              class="text-sm text-primary hover:underline flex items-center justify-end"
            >
              <ChevronRight class="h-4 w-4 mr-1 transform -rotate-90" />
              Back to Top
            </a>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
