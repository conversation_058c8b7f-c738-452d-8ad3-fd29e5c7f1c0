<script setup lang="ts">
import { IconLinkOff } from '@tabler/icons-vue'
import { computed } from 'vue'
const supportEmail = String(import.meta.env.VITE_COMPANY_SUPPORT_EMAIL || '')

// Build a mailto URL that includes subject and the current page URL in the body.
const mailto = computed(() => {
  const subject = encodeURIComponent('Help: Expired Link')
  const body = encodeURIComponent(
    `I followed a link that says it has expired.\n\nPage: ${window.location.href}\n\nPlease help.`,
  )
  return `mailto:${supportEmail}?subject=${subject}&body=${body}`
})
</script>

<template>
  <div class="min-h-screen flex items-center justify-center bg-slate-50 p-6">
    <div class="max-w-2xl w-full bg-white shadow-lg rounded-2xl p-8 sm:p-12 text-slate-900">
      <div class="flex flex-col items-center gap-8">
        <!-- Illustration -->
        <div
          class="flex-shrink-0 w-40 h-40 sm:w-48 sm:h-48 rounded-lg bg-gradient-to-br from-rose-50 to-amber-50 flex items-center justify-center"
        >
          <IconLinkOff class="w-24 h-24 text-rose-600" stroke-width="1.5" />
        </div>

        <div class="flex-1 text-center">
          <h1 class="text-3xl sm:text-4xl font-extrabold tracking-tight">This link has expired</h1>
          <p class="mt-3 text-slate-600">
            The link you followed is no longer valid. It may have been used already or it expired.
            If you need help, contact our support team below.
          </p>

          <div class="mt-6 flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4 justify-center">
            <!-- <a
              :href="mailto"
              class="inline-flex items-center justify-center gap-2 px-4 py-2 bg-rose-600 hover:bg-rose-700 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-rose-500"
              aria-label="Contact support"
            >
              Contact support
              <IconArrowRight class="w-5 h-5" />
            </a> -->

            <RouterLink
              to="/"
              class="inline-flex items-center justify-center gap-2 px-4 py-2 border border-slate-200 rounded-md hover:bg-slate-50"
            >
              Return to home
            </RouterLink>
          </div>

          <p class="mt-6 text-sm text-slate-500">
            Need help? Email us at
            <a :href="mailto" class="text-slate-700 font-bold">{{ supportEmail }}</a> for support.
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
