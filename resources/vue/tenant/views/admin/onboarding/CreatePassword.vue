<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { storeToRefs } from 'pinia'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Eye, EyeOff, Loader2, KeyRound } from 'lucide-vue-next'
import { useGlobalSettingsStore, useOnboardingStore, createPasswordSchema } from '@tenant/stores'
import { isEmptyObject } from '@/lib'

const globalStore = useGlobalSettingsStore()
const onboardingStore = useOnboardingStore()
const route = useRoute()
const router = useRouter()

const { isCreatingPassword, error, fieldErrors } = storeToRefs(onboardingStore)
const { createPassword } = onboardingStore

const showPassword = ref(false)
const showConfirmPassword = ref(false)

const form = useForm({
  validationSchema: toTypedSchema(createPasswordSchema),
  initialValues: {
    token: (route.params.token as string) || '',
    password: '',
    confirm_password: '',
  },
})

function applyFieldErrors() {
  const errors = fieldErrors.value

  if (!errors || typeof errors !== 'object') return

  Object.entries(errors).forEach(([key, msgs]) => {
    const first = Array.isArray(msgs) ? msgs[0] : String(msgs)
    form.setFieldError(key as any, first)
  })
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isCreatingPassword.value) return

  const result = await createPassword(values)

  if (result.success) {
    router.push({ name: 'admin-onboard-configure-app' })
  } else if (result.redirectToLogin) {
    router.push({ name: 'admin-login' })
  } else if (!isEmptyObject(fieldErrors.value)) {
    applyFieldErrors()
  }
})

onMounted(() => {
  const token = route.params.token as string
  if (!token) {
    router.push({ name: 'admin-login' })
  }
})
</script>

<template>
  <div class="flex flex-col min-h-screen w-full items-center justify-center px-4 py-8">
    <!-- Logo -->
    <div class="mb-8">
      <img
        v-if="globalStore.settings?.app_logo"
        :src="globalStore.settings?.app_logo"
        alt="logo"
        class="max-h-12 max-w-56 dark:invert"
      />
    </div>

    <!-- Form Card -->
    <Card class="w-full max-w-md">
      <CardHeader class="text-center pb-8">
        <div
          class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-4"
        >
          <KeyRound class="h-6 w-6 text-primary" />
        </div>
        <CardTitle class="text-2xl font-bold">Create New Password</CardTitle>
        <CardDescription>
          Set up your admin account password to access the admin panel
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="onSubmit" class="space-y-6">
          <!-- Error Alert -->
          <Alert v-if="error" variant="destructive">
            <AlertDescription>{{ error }}</AlertDescription>
          </Alert>

          <!-- Password Field -->
          <FormField name="password" v-slot="{ componentField, errorMessage }">
            <FormItem>
              <FormLabel>New Password</FormLabel>
              <FormControl>
                <div class="relative">
                  <Input
                    v-bind="componentField"
                    :type="showPassword ? 'text' : 'password'"
                    placeholder="Enter new password"
                    :aria-invalid="!!errorMessage"
                    class="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    @click="showPassword = !showPassword"
                    :aria-label="showPassword ? 'Hide password' : 'Show password'"
                  >
                    <Eye v-if="!showPassword" class="h-4 w-4 text-muted-foreground" />
                    <EyeOff v-else class="h-4 w-4 text-muted-foreground" />
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Confirm Password Field -->
          <FormField name="confirm_password" v-slot="{ componentField, errorMessage }">
            <FormItem>
              <FormLabel>Confirm Password</FormLabel>
              <FormControl>
                <div class="relative">
                  <Input
                    v-bind="componentField"
                    :type="showConfirmPassword ? 'text' : 'password'"
                    placeholder="Confirm your password"
                    :aria-invalid="!!errorMessage"
                    class="pr-10"
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    class="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    @click="showConfirmPassword = !showConfirmPassword"
                    :aria-label="showConfirmPassword ? 'Hide password' : 'Show password'"
                  >
                    <Eye v-if="!showConfirmPassword" class="h-4 w-4 text-muted-foreground" />
                    <EyeOff v-else class="h-4 w-4 text-muted-foreground" />
                  </Button>
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          </FormField>

          <!-- Submit Button -->
          <Button type="submit" class="w-full" :disabled="isCreatingPassword">
            <Loader2 v-if="isCreatingPassword" class="mr-2 h-4 w-4 animate-spin" />
            {{ isCreatingPassword ? 'Creating Password...' : 'Create Password' }}
          </Button>
        </form>
      </CardContent>
    </Card>

    <!-- Password Requirements -->
    <Card class="w-full max-w-md mt-6">
      <CardContent class="pt-6">
        <h3 class="text-sm font-semibold mb-3">Password Requirements:</h3>
        <ul class="text-sm text-muted-foreground space-y-1">
          <li class="flex items-center gap-2">
            <div class="w-1 h-1 rounded-full bg-muted-foreground"></div>
            At least 8 characters long
          </li>
          <li class="flex items-center gap-2">
            <div class="w-1 h-1 rounded-full bg-muted-foreground"></div>
            Mix of letters and numbers recommended
          </li>
        </ul>
      </CardContent>
    </Card>
  </div>
</template>
