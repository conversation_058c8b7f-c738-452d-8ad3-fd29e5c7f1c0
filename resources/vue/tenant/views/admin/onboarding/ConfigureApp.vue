<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import { storeToRefs } from 'pinia'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Settings2, Loader2, Image, ArrowRight, CheckCircle } from 'lucide-vue-next'
import { toast } from 'vue-sonner'
import {
  useGlobalSettingsStore,
  useOnboardingStore,
  onboardingAppSettingSchema,
} from '@tenant/stores'
import { isEmptyObject } from '@/lib'
import { z } from 'zod'

const globalStore = useGlobalSettingsStore()
const onboardingStore = useOnboardingStore()
const router = useRouter()

const { isUpdatingAppConfig, error, fieldErrors } = storeToRefs(onboardingStore)
const { updateAppConfig } = onboardingStore

const formError = ref<string | null>(null)
const logoInputRef = ref<HTMLInputElement | null>(null)
const faviconInputRef = ref<HTMLInputElement | null>(null)
const tempAppLogo = ref<string | null>(null)
const tempAppFavicon = ref<string | null>(null)
const isLogoLoading = ref(false)
const isFaviconLoading = ref(false)

function triggerLogoPicker() {
  logoInputRef.value?.click()
}

function triggerFaviconPicker() {
  faviconInputRef.value?.click()
}

function validateImageFile(file: File): boolean {
  if (file.size > 3 * 1024 * 1024) {
    toast.error('Please upload an image smaller than 3MB.')
    return false
  }
  return true
}

async function handleLogoChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return
  if (!validateImageFile(file)) {
    if (logoInputRef.value) logoInputRef.value.value = ''
    return
  }

  isLogoLoading.value = true
  const reader = new FileReader()
  reader.onload = () => {
    tempAppLogo.value = reader.result as string
    form.setFieldValue('app_logo', reader.result as string)
    isLogoLoading.value = false
    if (logoInputRef.value) logoInputRef.value.value = ''
  }
  reader.onerror = () => {
    toast.error('Failed to read image file')
    isLogoLoading.value = false
    if (logoInputRef.value) logoInputRef.value.value = ''
  }
  reader.readAsDataURL(file)
}

async function handleFaviconChange(event: Event) {
  const file = (event.target as HTMLInputElement)?.files?.[0]
  if (!file) return
  if (!validateImageFile(file)) {
    if (faviconInputRef.value) faviconInputRef.value.value = ''
    return
  }

  isFaviconLoading.value = true
  const reader = new FileReader()
  reader.onload = () => {
    tempAppFavicon.value = reader.result as string
    form.setFieldValue('app_favicon', reader.result as string)
    isFaviconLoading.value = false
    if (faviconInputRef.value) faviconInputRef.value.value = ''
  }
  reader.onerror = () => {
    toast.error('Failed to read image file')
    isFaviconLoading.value = false
    if (faviconInputRef.value) faviconInputRef.value.value = ''
  }
  reader.readAsDataURL(file)
}

const form = useForm({
  validationSchema: toTypedSchema(onboardingAppSettingSchema),
  initialValues: {
    app_name: '',
    app_logo: '',
    app_favicon: '',
  },
})

function applyFieldErrors() {
  const errors = fieldErrors.value

  if (!errors || typeof errors !== 'object') return

  Object.entries(errors).forEach(([key, msgs]) => {
    const first = Array.isArray(msgs) ? msgs[0] : String(msgs)
    form.setFieldError(key as any, first)
  })
}

const onSubmit = form.handleSubmit(async (values) => {
  if (isUpdatingAppConfig.value) return

  error.value = null
  fieldErrors.value = {}
  formError.value = null

  const payload: z.infer<typeof onboardingAppSettingSchema> = {
    app_name: values.app_name,
  }

  if (values.app_logo) {
    payload.app_logo = values.app_logo
  }

  if (values.app_favicon) {
    payload.app_favicon = values.app_favicon
  }

  const result = await updateAppConfig(payload)

  if (result.success) {
    toast.success('Pharmacy configuration completed successfully!')
    router.push({ name: 'admin-guide' })
  } else if (!isEmptyObject(fieldErrors.value)) {
    applyFieldErrors()
  } else {
    formError.value = error.value || 'Failed to update pharmacy configuration'
  }
})

const skipConfiguration = () => {
  toast.info('Configuration skipped - you can update these settings later in the Settings page')
  router.push({ name: 'admin-guide' })
}

onMounted(() => {
  if (globalStore.settings?.app_name) {
    form.setFieldValue('app_name', globalStore.settings.app_name)
  }
  if (globalStore.settings?.app_logo) {
    tempAppLogo.value = globalStore.settings.app_logo
  }
  if (globalStore.settings?.app_favicon) {
    tempAppFavicon.value = globalStore.settings.app_favicon
  }
})
</script>

<template>
  <div class="flex flex-col min-h-screen w-full items-center justify-center px-4 py-8">
    <!-- Logo -->
    <div class="mb-8">
      <img
        v-if="tempAppLogo || globalStore.settings?.app_logo"
        :src="tempAppLogo || globalStore.settings?.app_logo"
        alt="logo"
        class="max-h-12 max-w-56 dark:invert"
      />
    </div>

    <!-- Form Card -->
    <Card class="w-full max-w-2xl">
      <CardHeader class="text-center pb-8">
        <div
          class="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-primary/10 mb-4"
        >
          <Settings2 class="h-6 w-6 text-primary" />
        </div>
        <CardTitle class="text-2xl font-bold">Configure Your Pharmacy</CardTitle>
        <CardDescription>
          Basic configuration to get started. You can always change these later in settings.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form @submit.prevent="onSubmit" class="space-y-8">
          <!-- Error Alert -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Pharmacy Name -->
          <FormField name="app_name" v-slot="{ componentField, errorMessage }">
            <FormItem>
              <FormLabel>Pharmacy Name *</FormLabel>
              <FormControl>
                <Input
                  v-bind="componentField"
                  placeholder="Enter your pharmacy name"
                  :aria-invalid="!!errorMessage"
                />
              </FormControl>
              <FormMessage />
              <div class="text-xs text-muted-foreground mt-1">
                This will be displayed in browser titles and throughout your pharmacy application.
              </div>
            </FormItem>
          </FormField>

          <!-- Logo & Favicon Section -->
          <div class="space-y-6">
            <div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Pharmacy Logo -->
                <FormField name="app_logo">
                  <FormItem>
                    <FormLabel>Pharmacy Logo</FormLabel>
                    <FormControl>
                      <div class="flex items-center gap-4">
                        <div
                          class="h-20 w-36 rounded border overflow-hidden checkerboard flex items-center justify-center"
                        >
                          <template v-if="tempAppLogo">
                            <img
                              :src="tempAppLogo"
                              alt="Logo preview"
                              class="max-h-full max-w-full object-contain"
                            />
                          </template>
                          <template v-else>
                            <Image class="h-8 w-8 text-muted-foreground" />
                          </template>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          @click="triggerLogoPicker"
                          :disabled="isLogoLoading"
                        >
                          <Loader2 v-if="isLogoLoading" class="mr-2 h-4 w-4 animate-spin" />
                          {{ tempAppLogo ? 'Change Logo' : 'Select Logo' }}
                        </Button>
                        <input
                          ref="logoInputRef"
                          type="file"
                          accept="image/*"
                          class="hidden"
                          @change="handleLogoChange"
                        />
                      </div>
                    </FormControl>
                    <div class="text-xs text-muted-foreground mt-1">
                      Recommended dimensions: 200x50px. <br />
                      Max size: 3MB
                    </div>
                  </FormItem>
                </FormField>

                <!-- Pharmacy Favicon -->
                <FormField name="app_favicon">
                  <FormItem>
                    <FormLabel>Pharmacy Favicon</FormLabel>
                    <FormControl>
                      <div class="flex items-center gap-4">
                        <div
                          class="h-12 w-12 rounded border overflow-hidden checkerboard flex items-center justify-center"
                        >
                          <template v-if="tempAppFavicon">
                            <img
                              :src="tempAppFavicon"
                              alt="Favicon preview"
                              class="max-h-full max-w-full object-contain"
                            />
                          </template>
                          <template v-else>
                            <Image class="h-6 w-6 text-muted-foreground" />
                          </template>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          @click="triggerFaviconPicker"
                          :disabled="isFaviconLoading"
                        >
                          <Loader2 v-if="isFaviconLoading" class="mr-2 h-4 w-4 animate-spin" />
                          {{ tempAppFavicon ? 'Change Favicon' : 'Select Favicon' }}
                        </Button>
                        <input
                          ref="faviconInputRef"
                          type="file"
                          accept="image/*"
                          class="hidden"
                          @change="handleFaviconChange"
                        />
                      </div>
                    </FormControl>
                    <div class="text-xs text-muted-foreground mt-1">
                      Recommended dimensions: 32x32px. <br />
                      Max size: 3MB
                    </div>
                  </FormItem>
                </FormField>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-3 pt-6">
            <Button
              type="button"
              variant="outline"
              @click="skipConfiguration"
              class="order-2 sm:order-1"
            >
              Skip for Now
            </Button>
            <Button type="submit" class="order-1 sm:order-2 flex-1" :disabled="isUpdatingAppConfig">
              <Loader2 v-if="isUpdatingAppConfig" class="mr-2 h-4 w-4 animate-spin" />
              <template v-if="isUpdatingAppConfig"> Completing Setup... </template>
              <template v-else>
                Complete Setup
                <ArrowRight class="ml-2 h-4 w-4" />
              </template>
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>

    <!-- Getting Started Tips -->
    <Card class="w-full max-w-2xl mt-6">
      <CardContent>
        <h3 class="text-sm font-semibold mb-3 flex items-center gap-2">
          <CheckCircle class="h-4 w-4 text-green-600" />
          What's Next?
        </h3>
        <ul class="text-sm text-muted-foreground space-y-2">
          <li class="flex items-start gap-2">
            <div class="w-1 h-1 rounded-full bg-muted-foreground mt-2 flex-shrink-0"></div>
            <span>Access your admin dashboard to manage products and categories</span>
          </li>
          <li class="flex items-start gap-2">
            <div class="w-1 h-1 rounded-full bg-muted-foreground mt-2 flex-shrink-0"></div>
            <span>You can update these settings anytime from the Settings page</span>
          </li>
          <li class="flex items-start gap-2">
            <div class="w-1 h-1 rounded-full bg-muted-foreground mt-2 flex-shrink-0"></div>
            <span>Check out the Guide section for help getting started</span>
          </li>
        </ul>
      </CardContent>
    </Card>
  </div>
</template>

<style scoped>
.checkerboard {
  background-color: hsl(var(--background));
  background-image:
    linear-gradient(45deg, rgba(0, 0, 0, 0.06) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(0, 0, 0, 0.06) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(0, 0, 0, 0.06) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(0, 0, 0, 0.06) 75%);
  background-size: 16px 16px;
  background-position:
    0 0,
    0 8px,
    8px -8px,
    -8px 0px;
}

:deep(.dark) .checkerboard {
  background-color: hsl(var(--background));
  background-image:
    linear-gradient(45deg, rgba(255, 255, 255, 0.08) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.08) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.08) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.08) 75%);
}
</style>
