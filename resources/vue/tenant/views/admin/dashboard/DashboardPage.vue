<script setup lang="ts">
import { useAuthStore } from '@tenant/stores'

const authStore = useAuthStore()

const quickActions = [
  { title: 'Manage Categories', icon: '🏷️', path: '/admin/categories' },
  { title: 'Manage Products', icon: '📦', path: '/admin/products' },
  { title: 'Settings', icon: '⚙️', path: '/admin/settings' },
]
</script>

<template>
  <div class="w-full p-6 max-w-4xl mx-auto">
    <!-- Welcome Section -->
    <div class="text-center mb-12">
      <h1 class="text-3xl font-bold mb-2">
        Welcome back {{ authStore.user?.full_name?.split(' ')[0] }}!
      </h1>
      <p class="text-muted-foreground">Manage your store efficiently with these quick actions</p>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
      <button
        v-for="action in quickActions"
        :key="action.title"
        @click="$router.push(action.path)"
        class="group flex flex-col items-center justify-center p-6 rounded-xl border border-border hover:border-primary/50 hover:bg-accent/20 transition-all duration-200"
      >
        <span class="text-3xl mb-3 group-hover:scale-110 transition-transform">{{
          action.icon
        }}</span>
        <span class="font-medium">{{ action.title }}</span>
      </button>
    </div>
  </div>
</template>
