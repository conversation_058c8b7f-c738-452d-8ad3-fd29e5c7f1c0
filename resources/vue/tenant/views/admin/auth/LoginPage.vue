<script setup lang="ts">
import LoginForm from '@tenant/components/LoginForm.vue'
import { useGlobalSettingsStore } from '@tenant/stores'

const globalStore = useGlobalSettingsStore()
</script>

<template>
  <div class="flex flex-col h-screen w-full items-center justify-center px-4">
    <!-- logo -->
    <img
      :src="globalStore.settings?.app_logo"
      alt="logo"
      class="max-h-12 max-w-56 mb-6 dark:invert"
    />
    <LoginForm />
  </div>
</template>
