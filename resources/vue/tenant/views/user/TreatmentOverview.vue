<script setup lang="ts">
import { capitalize, formatCurrency } from '@/lib'
import Header from '@tenant/components/user/Header.vue'
import Footer from '@tenant/components/user/Footer.vue'
import ProductItem from '@tenant/components/user/ProductItem.vue'
import { computed, ref, watch, watchEffect } from 'vue'
import { useRoute } from 'vue-router'
import { IconArrowRight, IconMoodSad } from '@tabler/icons-vue'
import { useGlobalSettingsStore } from '@tenant/stores'
import { storeToRefs } from 'pinia'
import { usePublicProductStore } from '@tenant/stores'
import { toast } from 'vue-sonner'
import ProgramItemsModal from '@tenant/components/user/ProgramItemsModal.vue'
import PreVisitModal from '@tenant/components/user/PreVisitModal.vue'

const route = useRoute()
const globalStore = useGlobalSettingsStore()
const { content, settings } = storeToRefs(globalStore)

const productStore = usePublicProductStore()
const { productDetails, isLoadingDetails, detailsError } = storeToRefs(productStore)
const { fetchProductDetails } = usePublicProductStore()

const isModalOpen = ref(false)
const isPreVisitModalOpen = ref(false)

const relatedProducts = computed(() => {
  if (!productDetails.value) return [] as any[]
  return productDetails.value.recommendProducts
})

const productFAQs = ref<{ question: string; answer: string; open: boolean }[]>([])

watch(productDetails, (pd) => {
  if (pd && pd.product_faqs) {
    productFAQs.value = pd.product_faqs.map((faq: any) => ({ ...faq, open: false }))
  }
})

const toggle = (index: number) => {
  productFAQs.value = productFAQs.value.map((faq, i) => {
    if (i === index) {
      faq.open = !faq.open
    } else {
      faq.open = false
    }
    return faq
  })
}

const descriptionCardGradients = [
  {
    bgClasses: 'bg-white',
    titleClasses: 'text-neutral-900',
    textClasses: 'text-neutral-800',
  },
  {
    bgClasses: 'from-green-50 via-emerald-50 to-green-100',
    titleClasses: 'text-green-900',
    textClasses: 'text-green-800',
  },
  {
    bgClasses: 'from-blue-50 via-blue-50 to-blue-100',
    titleClasses: 'text-blue-900',
    textClasses: 'text-blue-800',
  },
  {
    bgClasses: 'from-amber-50 via-orange-50 to-amber-100',
    titleClasses: 'text-amber-900',
    textClasses: 'text-amber-800',
  },
  {
    bgClasses: 'from-purple-50 via-purple-50 to-purple-100',
    titleClasses: 'text-purple-900',
    textClasses: 'text-purple-800',
  },
  {
    bgClasses: 'from-pink-50 via-pink-50 to-pink-100',
    titleClasses: 'text-pink-900',
    textClasses: 'text-pink-800',
  },
  {
    bgClasses: 'from-red-50 via-red-50 to-red-100',
    titleClasses: 'text-red-900',
    textClasses: 'text-red-800',
  },
  {
    bgClasses: 'from-cyan-50 via-cyan-50 to-blue-100',
    titleClasses: 'text-cyan-900',
    textClasses: 'text-cyan-800',
  },
  {
    bgClasses: 'from-mint-50 via-mint-50 to-mint-100',
    titleClasses: 'text-mint-900',
    textClasses: 'text-mint-800',
  },
  {
    bgClasses: 'from-light-purple-50 via-light-purple-50 to-light-purple-100',
    titleClasses: 'text-light-purple-900',
    textClasses: 'text-light-purple-800',
  },
]

function getGradientForIndex(index: number) {
  // if (index === 0) {
  //   return descriptionCardGradients[0]
  // }
  return descriptionCardGradients[index % descriptionCardGradients.length]
}

watchEffect(async () => {
  await fetchProductDetails(route.params.slug as string)
  if (detailsError.value) {
    toast.error(detailsError.value)
  }

  document.title = productDetails.value
    ? `${productDetails.value.product_name_title} | ${settings.value ? settings.value.app_name : ''}`
    : 'Treatment Overview' + (settings.value ? ` | ${settings.value.app_name}` : '')
})
</script>

<template>
  <div class="bg-neutral-50">
    <Header :top-banner-content="content.topBanner" :is-admin="false" />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading State -->
      <div v-if="isLoadingDetails" class="bg-neutral-50 rounded-2xl shadow-sm p-8">
        <div class="animate-pulse">
          <div class="flex flex-col md:flex-row gap-8">
            <div class="w-full md:w-1/2 lg:w-2/5">
              <div class="bg-gray-200 rounded-xl aspect-square"></div>
            </div>
            <div class="w-full md:w-1/2 lg:w-3/5">
              <div class="h-8 bg-gray-200 rounded w-3/4 mb-4"></div>
              <div class="h-6 bg-gray-200 rounded w-1/4 mb-6"></div>
              <div class="h-6 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-full mb-2"></div>
              <div class="h-4 bg-gray-200 rounded w-3/4 mb-6"></div>
              <div class="h-10 bg-gray-200 rounded w-full mb-6"></div>
              <div class="h-12 bg-gray-200 rounded w-full"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- Product Details -->
      <div v-else-if="productDetails">
        <!-- Hero / Top Overview: full-bleed, no outer card -->
        <div
          :class="[
            'rounded-2xl p-6 sm:p-8 bg-gradient-to-br border border-cyan-100',
            'from-cyan-50 via-cyan-50 to-blue-100',
          ]"
        >
          <!-- Breadcrumbs -->
          <nav class="text-sm text-neutral-700 mb-4 flex items-center gap-1">
            <RouterLink :to="{ name: 'Home' }" class="hover:text-neutral-900">Home</RouterLink>
            <span>/</span>
            <RouterLink
              :to="{
                name: 'Treatments',
                params: { category_slug: productDetails.main_category_slug },
              }"
              class="hover:text-neutral-900"
              >{{ productDetails.main_category_name }}</RouterLink
            >
            <span>/</span>
            <span class="text-neutral-900 font-medium">{{
              productDetails.product_name_title
            }}</span>
          </nav>

          <div class="flex flex-col md:flex-row items-center gap-8">
            <!-- Product Image -->
            <div class="w-full md:w-1/2 lg:w-2/5 flex justify-center md:justify-start">
              <img
                :src="productDetails.image"
                :alt="productDetails.product_name_title"
                class="max-w-full max-h-[460px] object-contain"
              />
            </div>

            <!-- Product Info -->
            <div class="w-full md:w-1/2 lg:w-3/5">
              <h1 class="text-3xl sm:text-5xl font-bold text-neutral-900 mb-3">
                {{ productDetails.product_name_title }}
              </h1>

              <div class="flex items-center flex-wrap gap-2 mb-4">
                <template v-if="productDetails.product_type === 'single'">
                  <span
                    v-if="productDetails.product_form"
                    class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                    >{{ capitalize(productDetails.product_form) }}</span
                  >
                  <span
                    v-if="productDetails.product_strength"
                    class="px-3 py-1 bg-white/70 backdrop-blur text-gray-800 border border-neutral-200 rounded-full text-xs font-medium"
                    >{{ productDetails.product_strength }}
                  </span>
                </template>
              </div>

              <div class="flex items-baseline gap-2 mb-6">
                <span class="text-lg font-semibold text-neutral-900">
                  Starting at {{ formatCurrency(productDetails.price) }}
                </span>
              </div>

              <div class="flex flex-col sm:flex-row gap-3">
                <template v-if="productDetails.product_type === 'single'">
                  <button
                    @click.prevent="isPreVisitModalOpen = true"
                    class="w-full inline-flex justify-center items-center gap-2 bg-black text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-300 cursor-pointer text-center"
                  >
                    Continue Online Visit
                    <IconArrowRight class="w-5 h-5 me-2" stroke-width="2" />
                  </button>
                </template>
                <template v-if="productDetails.product_type === 'programs'">
                  <button
                    class="w-full inline-flex justify-center items-center gap-2 bg-black text-white px-6 py-3 rounded-full hover:bg-neutral-800 transition-all duration-300 cursor-pointer text-center"
                    @click.prevent="isModalOpen = true"
                  >
                    Continue Online Visit
                    <IconArrowRight class="w-5 h-5 me-2" stroke-width="2" />
                  </button>
                </template>
              </div>
            </div>
          </div>
        </div>

        <div class="py-12 space-y-12">
          <!-- Product Descriptions -->
          <div
            v-for="(description, index) in productDetails.product_descriptions"
            :key="description.title"
            class="bg-gradient-to-br rounded-3xl p-8"
            :class="getGradientForIndex(index).bgClasses"
          >
            <div class="flex items-center gap-3 mb-6">
              <h2
                class="text-2xl md:text-3xl font-bold"
                :class="getGradientForIndex(index).titleClasses"
              >
                {{ description.title }}
              </h2>
            </div>
            <div
              class="prose prose-sm md:prose-base prose-slate max-w-none leading-normal"
              :class="getGradientForIndex(index).textClasses"
              v-html="description.description"
            ></div>
          </div>

          <!-- FAQs -->
          <div v-if="productFAQs.length > 0" class="grid grid-cols-1 lg:grid-cols-2 gap-12 py-12">
            <div class="lg:sticky lg:top-32">
              <h2 class="text-4xl lg:text-5xl font-medium text-gray-900 mb-6">
                Frequently asked questions
              </h2>
              <p class="text-lg text-gray-600 mb-8">
                Find answers to common questions about our medication subscriptions and medical
                review process.
              </p>
            </div>
            <div class="space-y-4">
              <div
                v-for="(faq, idx) in productFAQs"
                :key="idx"
                class="border border-gray-200 rounded-lg"
              >
                <button
                  @click="toggle(idx)"
                  class="w-full flex justify-between items-center p-6 text-left hover:bg-gray-50 transition-colors"
                >
                  <span class="font-semibold text-gray-900">
                    {{ faq.question }}
                  </span>
                  <svg
                    class="w-6 h-6 text-gray-500 transition-transform duration-200"
                    :class="{ 'rotate-45': faq.open }"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                    ></path>
                  </svg>
                </button>
                <div
                  class="overflow-hidden transition-all duration-300 ease-in-out"
                  :class="{ 'max-h-96': faq.open, 'max-h-0': !faq.open }"
                >
                  <div class="px-6 pb-6 text-gray-600">
                    <p>{{ faq.answer }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Related Treatments -->
          <div v-if="relatedProducts.length">
            <h2 class="text-2xl md:text-4xl font-semibold text-neutral-900 text-center mb-8">
              Related Treatments
            </h2>
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ProductItem v-for="p in relatedProducts" :key="p.slug" :product="p" />
            </div>
          </div>
        </div>
      </div>

      <!-- Product Not Found -->
      <div v-else class="bg-neutral-50 rounded-2xl shadow-sm p-12 text-center">
        <div class="max-w-md mx-auto">
          <IconMoodSad class="w-16 h-16 text-gray-400 mx-auto mb-4" stroke-width="2" />
          <h3 class="text-xl font-semibold text-gray-900 mb-2">Treatment not found</h3>
          <p class="text-gray-600 mb-6">
            The treatment you are looking for does not exist or has been removed.
          </p>
          <RouterLink
            :to="{ name: 'Treatments' }"
            class="bg-black text-white px-6 py-3 rounded-full hover:bg-gray-800 transition-all duration-300 cursor-pointer"
          >
            Browse Treatments
          </RouterLink>
        </div>
      </div>
    </div>

    <Footer :content="content.footer" :is-admin="false" />
    <ProgramItemsModal
      v-if="productDetails && productDetails.product_type === 'programs'"
      :open="isModalOpen"
      :product-name="productDetails.product_name_title"
      :items="productDetails.product_items"
      @update:open="isModalOpen = $event"
    />

    <PreVisitModal
      v-if="productDetails && productDetails.product_type === 'single'"
      :open="isPreVisitModalOpen"
      :product-item-id="productDetails.product_item_id"
      :xpedicare-url="productDetails.xpedicare_url"
      @update:open="isPreVisitModalOpen = $event"
    />
  </div>
</template>
