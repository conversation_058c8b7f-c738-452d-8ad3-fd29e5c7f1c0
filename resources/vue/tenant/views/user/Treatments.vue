<script setup lang="ts">
import Header from '@tenant/components/user/Header.vue'
import { ref, computed, onMounted, watch } from 'vue'
import ProductItem from '@tenant/components/user/ProductItem.vue'
import { useRoute, useRouter } from 'vue-router'
import {
  IconChevronLeft,
  IconChevronRight,
  IconChevronDown,
  IconChevronUp,
  IconMenu2,
  IconMoodSad,
  IconSearch,
  IconX,
} from '@tabler/icons-vue'
import { usePublicProductStore } from '@tenant/stores'
import { storeToRefs } from 'pinia'
import Footer from '@tenant/components/user/Footer.vue'
import { useGlobalSettingsStore } from '@tenant/stores'
import { refDebounced } from '@vueuse/core'

const globalStore = useGlobalSettingsStore()
const { content } = storeToRefs(globalStore)

const productStore = usePublicProductStore()
const { products, pagination, isLoadingList, categories, gender } = storeToRefs(productStore)

const route = useRoute()
const router = useRouter()

const searchQuery = ref('')
const debouncedSearchQuery = refDebounced(searchQuery, 300)
const selectedCategory = ref<string | null>(null)
const selectedSubCategory = ref<string | null>(null)

const isMobileCategoryMenuOpen = ref(false)
const expandedCategories = ref<Record<string, boolean>>({})

const totalPages = computed(() => pagination.value.totalPages)

function toggleMobileCategoryMenu() {
  isMobileCategoryMenuOpen.value = !isMobileCategoryMenuOpen.value
}

function closeMobileCategoryMenu() {
  setTimeout(() => {
    isMobileCategoryMenuOpen.value = false
  }, 50)
}

function toggleCategoryExpansion(categorySlug: string) {
  expandedCategories.value = {
    ...expandedCategories.value,
    [categorySlug]: !expandedCategories.value[categorySlug],
  }
}

function selectGender(selectedGender: 'Male' | 'Female') {
  gender.value = selectedGender
  pagination.value.currentPage = 1
  fetchProducts()
  closeMobileCategoryMenu()
}

function clearGenderFilter() {
  gender.value = undefined
  pagination.value.currentPage = 1
  fetchProducts()
  closeMobileCategoryMenu()
}

function selectCategory(categorySlug: string) {
  selectedCategory.value = categorySlug
  selectedSubCategory.value = null
  searchQuery.value = ''
  pagination.value.currentPage = 1
  router.push({ name: 'Treatments', params: { category_slug: categorySlug } })
  closeMobileCategoryMenu()
}

function selectSubCategory(categorySlug: string, subCategorySlug: string) {
  selectedCategory.value = categorySlug
  selectedSubCategory.value = subCategorySlug
  searchQuery.value = ''
  pagination.value.currentPage = 1
  router.push({
    name: 'Treatments',
    params: { category_slug: categorySlug, sub_category_slug: subCategorySlug },
  })
  closeMobileCategoryMenu()
}

function clearFilters() {
  selectedCategory.value = null
  selectedSubCategory.value = null
  searchQuery.value = ''
  gender.value = undefined
  pagination.value.currentPage = 1
  router.push({ name: 'Treatments', params: { category_slug: null, sub_category_slug: null } })
  closeMobileCategoryMenu()
}

async function changePage(page: number) {
  if (page >= 1 && page <= totalPages.value) {
    pagination.value.currentPage = page
    await productStore.fetchProducts()
  }
}

async function fetchProducts() {
  let slug
  if (selectedSubCategory.value) {
    slug = selectedSubCategory.value
  } else if (selectedCategory.value) {
    slug = selectedCategory.value
  }
  productStore.slug = slug
  productStore.searchQuery = searchQuery.value
  await productStore.fetchProducts()
}

watch(debouncedSearchQuery, () => {
  if (debouncedSearchQuery.value) {
    selectedCategory.value = null
    selectedSubCategory.value = null
    router.push({ name: 'Treatments', params: { category_slug: null, sub_category_slug: null } })
  }
  pagination.value.currentPage = 1
  fetchProducts()
})

watch(
  () => route.params,
  (newParams) => {
    selectedCategory.value = (newParams.category_slug as string) || null
    selectedSubCategory.value = (newParams.sub_category_slug as string) || null
    if (selectedCategory.value) {
      expandedCategories.value[selectedCategory.value] = true
    }
    fetchProducts()
  },
  { immediate: true },
)

onMounted(async () => {
  await productStore.fetchAllCategories()
})

const currentCategoryName = computed(() => {
  if (!selectedCategory.value) return 'All'
  return categories.value.find((c) => c.slug === selectedCategory.value)?.name
})

const currentSubCategoryName = computed(() => {
  if (!selectedCategory.value || !selectedSubCategory.value) return ''
  const category = categories.value.find((c) => c.slug === selectedCategory.value)
  return category?.sub_categories.find((sc) => sc.slug === selectedSubCategory.value)?.name
})
</script>

<template>
  <div class="bg-neutral-50 min-h-screen">
    <Header :top-banner-content="content.topBanner" :is-admin="false" />

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-8 pb-16">
      <!-- Search Section -->
      <div class="mb-8">
        <div class="relative max-w-xl mx-auto">
          <input
            v-model="searchQuery"
            class="w-full px-6 py-4 text-lg bg-white border border-neutral-200 rounded-full focus:outline-none focus:ring-2 focus:ring-neutral-500 focus:border-transparent transition-all duration-200"
            type="text"
            placeholder="Search for treatments..."
          />
          <button
            v-if="searchQuery"
            class="absolute right-12 top-1/2 -translate-y-1/2 p-1 text-neutral-500 hover:text-neutral-700 focus:outline-none rounded-full"
            @click="searchQuery = ''"
          >
            <IconX class="w-5 h-5" stroke-width="2" />
          </button>
          <div class="absolute right-4 top-1/2 -translate-y-1/2">
            <IconSearch class="w-6 h-6 text-neutral-400" stroke-width="2" />
          </div>
        </div>
      </div>

      <div class="flex flex-col lg:flex-row gap-8">
        <!-- Desktop Categories Sidebar -->
        <div class="hidden lg:block lg:w-72 lg:sticky lg:top-24 lg:h-[calc(100vh-8rem)]">
          <div class="bg-white rounded-xl border border-neutral-200 p-6 h-full overflow-y-auto">
            <h3
              class="text-xl font-semibold text-neutral-900 mb-4 flex justify-between items-center"
            >
              <span>For</span>
              <button
                v-if="gender"
                @click="clearGenderFilter"
                class="p-1 text-neutral-500 hover:text-neutral-700 flex items-center"
              >
                <IconX class="w-4 h-4" /><span class="text-xs">Clear</span>
              </button>
            </h3>
            <div class="flex gap-2 mb-6">
              <button
                @click="selectGender('Male')"
                :class="[
                  gender === 'Male'
                    ? 'bg-neutral-800 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200',
                ]"
                class="flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Men
              </button>
              <button
                @click="selectGender('Female')"
                :class="[
                  gender === 'Female'
                    ? 'bg-neutral-800 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200',
                ]"
                class="flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Women
              </button>
            </div>
            <div class="border-t border-neutral-200 mb-6"></div>
            <h3 class="text-xl font-semibold text-neutral-900 mb-6">Categories</h3>
            <ul class="space-y-2">
              <li class="space-y-2">
                <div
                  class="flex items-center justify-between px-4 py-3 cursor-pointer rounded-lg transition-all duration-200"
                  :class="{
                    'bg-neutral-100 text-neutral-800 font-semibold': !selectedCategory,
                    'text-neutral-600 hover:bg-neutral-50': selectedCategory,
                  }"
                  @click="clearFilters"
                >
                  <span>All</span>
                </div>
              </li>
              <li v-for="category in categories" :key="category.slug" class="space-y-2">
                <div
                  class="flex items-center justify-between px-4 py-3 cursor-pointer rounded-lg transition-all duration-200"
                  :class="{
                    'bg-neutral-100 text-neutral-800 font-semibold':
                      selectedCategory === category.slug && !selectedSubCategory,
                    'text-neutral-600 hover:bg-neutral-50':
                      selectedCategory !== category.slug || selectedSubCategory,
                  }"
                  @click="selectCategory(category.slug)"
                >
                  <span class="flex flex-col">
                    {{ category.name }}
                    <span v-if="category.gender" class="text-xs text-gray-500 font-medium">
                      {{ category.gender === 'Male' ? 'For Men' : 'For Women' }}
                    </span>
                  </span>
                  <button
                    v-if="category.sub_categories.length > 0"
                    class="p-1 bg-neutral-100 text-neutral-500 hover:text-neutral-700 focus:outline-none rounded-full"
                    @click.stop="toggleCategoryExpansion(category.slug)"
                  >
                    <IconChevronUp
                      v-if="expandedCategories[category.slug]"
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                    <IconChevronDown v-else class="w-4 h-4" stroke-width="2" />
                  </button>
                </div>
                <transition
                  enter-active-class="transition-all duration-300 ease-out"
                  enter-from-class="max-h-0 opacity-0"
                  enter-to-class="max-h-[500px] opacity-100"
                  leave-active-class="transition-all duration-200 ease-in"
                  leave-from-class="max-h-[500px] opacity-100"
                  leave-to-class="max-h-0 opacity-0"
                >
                  <ul
                    v-if="category.sub_categories.length > 0 && expandedCategories[category.slug]"
                    class="ps-6 space-y-1 overflow-hidden"
                  >
                    <li
                      v-for="subCategory in category.sub_categories"
                      :key="subCategory.slug"
                      class="px-4 py-2 cursor-pointer rounded-lg transition-all duration-200 text-sm"
                      :class="{
                        'bg-neutral-100 text-neutral-800 font-semibold':
                          selectedCategory === category.slug &&
                          selectedSubCategory === subCategory.slug,
                        'text-neutral-600 hover:bg-neutral-50':
                          selectedCategory !== category.slug ||
                          selectedSubCategory !== subCategory.slug,
                      }"
                      @click="selectSubCategory(category.slug, subCategory.slug)"
                    >
                      {{ subCategory.name }}
                    </li>
                  </ul>
                </transition>
              </li>
            </ul>
          </div>
        </div>

        <!-- Main Content Area -->
        <div class="flex-grow lg:w-[calc(100%-18rem)]">
          <!-- Top Strip -->
          <div
            class="bg-white rounded-xl border border-neutral-200 p-4 mb-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0"
          >
            <div class="flex items-center gap-2 w-full sm:w-auto overflow-hidden">
              <div class="flex items-center gap-1">
                <button
                  class="lg:hidden p-1 text-neutral-600 hover:text-neutral-900 focus:outline-none"
                  @click="toggleMobileCategoryMenu"
                >
                  <IconMenu2 class="w-5 h-5" stroke-width="2" />
                </button>
                <span class="text-sm text-neutral-600 whitespace-nowrap">Showing:</span>
              </div>
              <span class="font-semibold text-neutral-900 truncate text-sm">
                {{ currentCategoryName }}
                <span v-if="currentSubCategoryName">
                  <span class="text-neutral-600 text-xs">/</span>
                  {{ currentSubCategoryName }}
                </span>
              </span>
            </div>
          </div>

          <!-- Loading State -->
          <div
            v-if="isLoadingList"
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6"
          >
            <div
              v-for="i in 6"
              :key="i"
              class="bg-white rounded-2xl border border-neutral-200 overflow-hidden"
            >
              <div class="animate-pulse">
                <div class="pt-[100%] bg-neutral-200"></div>
                <div class="p-4 sm:p-6">
                  <div class="h-6 bg-neutral-200 rounded w-3/4 mb-4"></div>
                  <div class="h-4 bg-neutral-200 rounded w-full mb-4"></div>
                  <div class="h-4 bg-neutral-200 rounded w-1/2 mb-4"></div>
                  <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
                    <div class="h-6 bg-neutral-200 rounded w-1/4"></div>
                    <div class="h-10 bg-neutral-200 rounded w-1/3 hidden sm:block"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Products Grid -->
          <div v-else>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ProductItem v-for="product in products" :key="product.slug" :product="product" />
            </div>
          </div>

          <!-- Empty State -->
          <div
            v-if="!isLoadingList && products.length === 0"
            class="bg-white rounded-2xl border border-neutral-200 p-12 text-center"
          >
            <div class="max-w-md mx-auto">
              <IconMoodSad class="w-16 h-16 text-neutral-400 mx-auto mb-4" stroke-width="2" />
              <h3 class="text-xl font-semibold text-neutral-900 mb-2">No treatments found</h3>
              <p class="text-neutral-600 mb-6">
                Try adjusting your search or category filters to find what you're looking for.
              </p>
              <button
                class="!px-6 !py-2.5 bg-neutral-800 text-white rounded-full"
                @click="clearFilters"
              >
                Clear Filters
              </button>
            </div>
          </div>

          <!-- Pagination Strip -->
          <div
            v-if="!isLoadingList && products.length > 0"
            class="bg-white rounded-xl border border-neutral-200 p-4 mt-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 sm:gap-0"
          >
            <div class="flex items-center gap-2 sm:gap-4">
              <span class="text-neutral-600 text-sm sm:text-base">Show</span>
              <select
                v-model="pagination.perPage"
                class="w-14 px-2 py-1.5 border !border-neutral-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-neutral-600 focus:!border-transparent"
                @change="fetchProducts"
              >
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="30">30</option>
              </select>
              <span class="text-neutral-600 text-sm sm:text-base">per page</span>
            </div>
            <div
              class="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4 w-full sm:w-auto"
            >
              <span class="text-neutral-600 text-sm sm:text-base order-2 sm:order-1">
                {{ (pagination.currentPage - 1) * pagination.perPage + 1 }} -
                {{ Math.min(pagination.currentPage * pagination.perPage, pagination.totalRecords) }}
                of {{ pagination.totalRecords }}
              </span>
              <div class="flex gap-2 self-end sm:self-auto order-1 sm:order-2">
                <button
                  class="p-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="
                    pagination.currentPage === 1
                      ? 'text-neutral-400'
                      : 'text-neutral-600 hover:bg-neutral-50'
                  "
                  :disabled="pagination.currentPage === 1"
                  @click="changePage(pagination.currentPage - 1)"
                >
                  <IconChevronLeft class="w-5 h-5" stroke-width="2" />
                </button>
                <button
                  class="p-2 rounded-lg disabled:opacity-50 disabled:cursor-not-allowed"
                  :class="
                    pagination.currentPage === totalPages
                      ? 'text-neutral-400'
                      : 'text-neutral-600 hover:bg-neutral-50'
                  "
                  :disabled="pagination.currentPage === totalPages"
                  @click="changePage(pagination.currentPage + 1)"
                >
                  <IconChevronRight class="w-5 h-5" stroke-width="2" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile Category Menu Overlay -->
    <transition
      enter-active-class="transition-opacity duration-300 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-200 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isMobileCategoryMenuOpen"
        class="fixed inset-0 bg-neutral-900 bg-opacity-50 z-50 lg:hidden flex flex-col"
      >
        <div class="bg-white h-full w-full overflow-auto animate-slide-in-right">
          <div class="p-4 flex justify-between items-center border-b border-neutral-200">
            <h3 class="text-xl font-semibold text-neutral-900">Categories</h3>
            <button
              class="p-2 text-neutral-600 hover:text-neutral-900 focus:outline-none"
              @click="closeMobileCategoryMenu"
            >
              <IconX class="w-6 h-6" stroke-width="2" />
            </button>
          </div>

          <div class="p-4 overflow-y-auto max-h-[calc(100vh-4rem)]">
            <h3
              class="text-lg font-semibold text-neutral-900 mb-4 flex justify-between items-center"
            >
              <span>For</span>
              <button
                v-if="gender"
                @click="clearGenderFilter"
                class="p-1 text-neutral-500 hover:text-neutral-700 flex items-center"
              >
                <IconX class="w-4 h-4" /><span class="text-xs">Clear</span>
              </button>
            </h3>
            <div class="flex gap-2 mb-6">
              <button
                @click="selectGender('Male')"
                :class="[
                  gender === 'Male'
                    ? 'bg-neutral-800 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200',
                ]"
                class="flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Men
              </button>
              <button
                @click="selectGender('Female')"
                :class="[
                  gender === 'Female'
                    ? 'bg-neutral-800 text-white'
                    : 'bg-neutral-100 text-neutral-700 hover:bg-neutral-200',
                ]"
                class="flex-1 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Women
              </button>
            </div>
            <div class="border-t border-neutral-200 mb-6"></div>
            <ul class="space-y-2">
              <li class="space-y-2">
                <div
                  class="flex items-center justify-between px-4 py-3 cursor-pointer rounded-lg transition-all duration-200 active:bg-neutral-200"
                  :class="{
                    'bg-neutral-100 text-neutral-800 font-semibold': !selectedCategory,
                    'text-neutral-600 hover:bg-neutral-50': selectedCategory,
                  }"
                  @click="clearFilters"
                >
                  <span>All</span>
                </div>
              </li>
              <li v-for="category in categories" :key="category.slug" class="space-y-2">
                <div
                  class="flex items-center justify-between px-4 py-3 cursor-pointer rounded-lg transition-all duration-200 active:bg-neutral-200"
                  :class="{
                    'bg-neutral-100 text-neutral-800 font-semibold':
                      selectedCategory === category.slug && !selectedSubCategory,
                    'text-neutral-600 hover:bg-neutral-50':
                      selectedCategory !== category.slug || selectedSubCategory,
                  }"
                  @click="selectCategory(category.slug)"
                >
                  <span>{{ category.name }}</span>
                  <button
                    v-if="category.sub_categories.length > 0"
                    class="p-1 bg-neutral-100 text-neutral-500 hover:text-neutral-700 focus:outline-none rounded-full"
                    @click.stop="toggleCategoryExpansion(category.slug)"
                  >
                    <IconChevronUp
                      v-if="expandedCategories[category.slug]"
                      class="w-4 h-4"
                      stroke-width="2"
                    />
                    <IconChevronDown v-else class="w-4 h-4" stroke-width="2" />
                  </button>
                </div>
                <transition
                  enter-active-class="transition-all duration-300 ease-out"
                  enter-from-class="max-h-0 opacity-0"
                  enter-to-class="max-h-[500px] opacity-100"
                  leave-active-class="transition-all duration-200 ease-in"
                  leave-from-class="max-h-[500px] opacity-100"
                  leave-to-class="max-h-0 opacity-0"
                >
                  <ul
                    v-if="category.sub_categories.length > 0 && expandedCategories[category.slug]"
                    class="ps-6 space-y-1 overflow-hidden"
                  >
                    <li
                      v-for="subCategory in category.sub_categories"
                      :key="subCategory.slug"
                      class="px-4 py-2 cursor-pointer rounded-lg transition-all duration-200 text-sm active:bg-neutral-200"
                      :class="{
                        'bg-neutral-100 text-neutral-800 font-semibold':
                          selectedCategory === category.slug &&
                          selectedSubCategory === subCategory.slug,
                        'text-neutral-600 hover:bg-neutral-50':
                          selectedCategory !== category.slug ||
                          selectedSubCategory !== subCategory.slug,
                      }"
                      @click="selectSubCategory(category.slug, subCategory.slug)"
                    >
                      {{ subCategory.name }}
                    </li>
                  </ul>
                </transition>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </transition>

    <Footer :content="content.footer" :is-admin="false" />
  </div>
</template>

<style scoped>
@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out forwards;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 10px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
