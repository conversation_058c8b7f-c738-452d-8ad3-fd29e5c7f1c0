import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiClient } from '@tenant/composables'
import type { ApiResponse } from '@/types/api'
import type { AppSettings, Category, MainCategory, Product } from '@tenant/types'
import { processErrors } from '@/lib'
import { toast } from 'vue-sonner'

type LPCategory = Pick<Category, 'name' | 'slug' | 'image' | 'description' | 'gender'>

type LPDataResponse = ApiResponse & {
  data: {
    categories: LPCategory[]
    topProducts: Product[]
  }
}

type HeaderResponse = ApiResponse & {
  mainCategories: MainCategory[]
  topProducts: Product[]
}

export const useGlobalSettingsStore = defineStore('globalSettings', () => {
  const settings = ref<AppSettings | null>(null)
  const loading = ref(false)
  const error = ref<Error | null>(null)

  const content = ref<any>(null)
  const hasChanges = ref(false)
  const isSaving = ref(false)
  let debounceTimer: any = null

  const isLoadingLPData = ref(false)
  const categories = ref<LPCategory[]>([])
  const topProducts = ref<Product[]>([])
  const lpDataError = ref<string | null>(null)

  const isLoadingHeaderData = ref(false)
  const mainCategories = ref<MainCategory[]>([])
  const headerTopProducts = ref<Product[]>([])
  const headerError = ref<string | null>(null)

  async function loadSettings() {
    loading.value = true
    error.value = null
    try {
      const { data } = await apiClient.get<ApiResponse & { app_settings: AppSettings }>(
        '/app-settings',
      )
      if (data.status === 200) {
        settings.value = data.app_settings
        content.value = data.app_settings.lp_data
        applyHeadTags(data.app_settings)
      }
    } catch (err: any) {
      error.value = err
    } finally {
      loading.value = false
    }
  }

  function applyHeadTags(appSettings: any) {
    if (!appSettings) return

    const favicon = appSettings.app_favicon || undefined

    // Clear old icons
    const oldIcons = document.querySelectorAll(
      "link[rel='icon'], link[rel='apple-touch-icon'], meta[name='msapplication-TileImage']",
    )
    oldIcons.forEach((el) => el.parentNode?.removeChild(el))

    // Add new <link rel="icon">
    const icon32 = document.createElement('link')
    icon32.rel = 'icon'
    icon32.href = favicon
    icon32.sizes = '32x32'
    document.head.appendChild(icon32)

    const icon192 = document.createElement('link')
    icon192.rel = 'icon'
    icon192.href = favicon
    icon192.sizes = '192x192'
    document.head.appendChild(icon192)

    // Apple Touch Icon
    const appleTouch = document.createElement('link')
    appleTouch.rel = 'apple-touch-icon'
    appleTouch.href = favicon
    document.head.appendChild(appleTouch)

    // Microsoft Tile
    const msTile = document.createElement('meta')
    msTile.name = 'msapplication-TileImage'
    msTile.content = favicon
    document.head.appendChild(msTile)
  }

  async function fetchLandingPageData() {
    isLoadingLPData.value = true
    lpDataError.value = null

    try {
      const { data } = await apiClient.get<LPDataResponse>('/fetch-landing-page-data')
      if (data.status === 200) {
        categories.value = data.data.categories
        topProducts.value = data.data.topProducts
      } else {
        lpDataError.value = data.message || 'Failed to fetch landing page data'
      }
    } catch (err) {
      console.error('Error fetching landing page data:', err)
      lpDataError.value = processErrors(err, 'Failed to fetch landing page data')
    } finally {
      isLoadingLPData.value = false
    }
  }

  function updateSection(section: string, newContent: any) {
    content.value[section] = newContent
    hasChanges.value = true

    clearTimeout(debounceTimer)
    debounceTimer = setTimeout(() => {
      saveContent()
    }, 2000) // 2-second delay
  }

  async function saveContent() {
    isSaving.value = true
    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-app-configs', content.value)
      if (response.status === 200) {
        toast.success('Content saved successfully!', {
          position: 'bottom-right',
        })
        hasChanges.value = false
        // loadSettings()
      } else {
        toast.error(response.data.message || 'Failed to save content.')
      }
    } catch (err) {
      console.error('Error saving content:', err)
      toast.error(processErrors(err, 'Failed to save content.'))
    } finally {
      isSaving.value = false
    }
  }

  async function fetchHeaderData() {
    if (mainCategories.value.length > 0) return

    isLoadingHeaderData.value = true
    headerError.value = null

    try {
      const { data } = await apiClient.get<HeaderResponse>('/get-header-categories')
      if (data.status === 200) {
        mainCategories.value = data.mainCategories
        headerTopProducts.value = data.topProducts
      } else {
        headerError.value = data.message || 'Failed to fetch header data'
      }
    } catch (err) {
      console.error('Error fetching header data:', err)
      headerError.value = processErrors(err, 'Failed to fetch header data')
    } finally {
      isLoadingHeaderData.value = false
    }
  }

  return {
    // state
    settings,
    loading,
    error,
    content,
    hasChanges,
    isSaving,
    isLoadingLPData,
    categories,
    topProducts,
    lpDataError,
    isLoadingHeaderData,
    mainCategories,
    headerTopProducts,
    headerError,

    // actions
    loadSettings,
    fetchLandingPageData,
    updateSection,
    saveContent,
    fetchHeaderData,
  }
})
