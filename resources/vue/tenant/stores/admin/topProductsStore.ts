import { apiClient } from '@tenant/composables'
import { processErrors } from '@/lib'
import type { ApiResponse } from '@/types/api'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { toast } from 'vue-sonner'

type TopProductItem = {
  top_product_id: string
  product_id: string
  product_title: string | null
  product_type: 'single' | 'programs'
  main_category_name: string
  product_name_title: string
  image: string
}

type ProductItem = {
  id: string
  main_category_name: string
  sub_category_name: string | null
  product_name_title: string
  product_form: string
}

export const useTopProductsStore = defineStore('topProducts', () => {
  // state
  const topProducts = ref<TopProductItem[]>([])
  const allProducts = ref<ProductItem[]>([])
  const isLoading = ref(false)
  const isLoadingAllProducts = ref(false)
  const error = ref<string | null>(null)
  const isAdding = ref(false)
  const isDeleting = ref(false)
  const isReordering = ref(false)

  async function fetchTopProducts() {
    isLoading.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { topProducts: TopProductItem[] }>(
        '/admin/list/top-products',
      )
      if (response.data.status === 200) {
        topProducts.value = response.data.topProducts
        return true
      } else {
        error.value = response.data.message || 'Failed to fetch top products'
        return false
      }
    } catch (err) {
      error.value = processErrors(err, 'Failed to fetch top products')
      return false
    } finally {
      isLoading.value = false
    }
  }

  async function fetchAllProducts() {
    isLoadingAllProducts.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse & { allProducts: ProductItem[] }>(
        '/admin/all-products',
      )
      if (response.data.status === 200) {
        allProducts.value = response.data.allProducts
        return true
      } else {
        error.value = response.data.message || 'Failed to fetch all products'
        return false
      }
    } catch (err) {
      error.value = processErrors(err, 'Failed to fetch all products')
      return false
    } finally {
      isLoadingAllProducts.value = false
    }
  }

  async function addTopProduct(productId: string) {
    isAdding.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/add/top-product/${productId}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Top product added successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to add top product'
        return false
      }
    } catch (err) {
      error.value = processErrors(err, 'Failed to add top product')
      return false
    } finally {
      isAdding.value = false
    }
  }

  async function deleteTopProduct(productId: string) {
    isDeleting.value = true
    error.value = null

    try {
      const response = await apiClient.get<ApiResponse>(`/admin/delete/top-product/${productId}`)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Top product removed successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to remove top product'
        return false
      }
    } catch (err) {
      error.value = processErrors(err, 'Failed to remove top product')
      return false
    } finally {
      isDeleting.value = false
    }
  }

  async function reorderTopProducts(productIds: string[]) {
    isReordering.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse>(`/admin/re-order/top-products`, {
        ids: productIds,
      })

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Top products reordered successfully')
        return true
      } else {
        error.value = response.data.message || 'Failed to reorder top products'
        return false
      }
    } catch (err) {
      error.value = processErrors(err, 'Failed to reorder top products')
      return false
    } finally {
      isReordering.value = false
    }
  }

  return {
    topProducts,
    isLoading,
    error,
    isAdding,
    isDeleting,
    isReordering,
    allProducts,
    isLoadingAllProducts,

    fetchTopProducts,
    fetchAllProducts,
    addTopProduct,
    deleteTopProduct,
    reorderTopProducts,
  }
})
