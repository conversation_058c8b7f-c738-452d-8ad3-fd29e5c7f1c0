import { defineStore } from 'pinia'
import { ref } from 'vue'
import { apiClient } from '@tenant/composables/useApi'
import type { ApiResponse } from '@/types/api'
import type { AppSettings } from '@tenant/types'
import { processErrors } from '@/lib'
import { z } from 'zod'
import { toast } from 'vue-sonner'
import { useGlobalSettingsStore } from '../globalSettingsStore'

export const appSettingSchema = z.object({
  app_name: z.string().min(1, 'Pharmacy name is required'),
})

export const useSettingsStore = defineStore('settings', () => {
  const globalSettingsStore = useGlobalSettingsStore()

  const settings = ref<AppSettings>({})
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})
  const isUpdatingAppSetting = ref(false)
  const isUpdatingLogo = ref(false)
  const isUpdatingFavicon = ref(false)

  const fetchSettings = async () => {
    isLoading.value = true
    error.value = null

    try {
      const response = await apiClient.get<
        ApiResponse & {
          app_settings: AppSettings
        }
      >('/admin/app-settings')

      if (response.data.status === 200 && response.data.app_settings) {
        settings.value = response.data.app_settings
      }
    } catch (err) {
      error.value = processErrors(err)[0]
    } finally {
      isLoading.value = false
    }
  }

  async function updateAppSetting(payload: z.infer<typeof appSettingSchema>) {
    isUpdatingAppSetting.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-app-settings', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy settings updated successfully')
        globalSettingsStore.loadSettings()
        return true
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return false
      } else {
        error.value = response.data.message || 'Failed to update pharmacy settings'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy settings:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy settings.')
      return false
    } finally {
      isUpdatingAppSetting.value = false
    }
  }

  async function updateLogo(payload: { image: string }) {
    isUpdatingLogo.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-app-logo', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy logo updated successfully')
        globalSettingsStore.loadSettings()
        return true
      } else {
        error.value = response.data.message || 'Failed to update pharmacy logo'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy logo:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy logo.')
      return false
    } finally {
      isUpdatingLogo.value = false
    }
  }

  async function updateFavicon(payload: { image: string }) {
    isUpdatingFavicon.value = true
    error.value = null

    try {
      const response = await apiClient.post<ApiResponse>('/admin/update-app-favicon', payload)

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy favicon updated successfully')
        globalSettingsStore.loadSettings()
        return true
      } else {
        error.value = response.data.message || 'Failed to update pharmacy favicon'
        return false
      }
    } catch (err: any) {
      console.error('Error updating pharmacy favicon:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy favicon.')
      return false
    } finally {
      isUpdatingFavicon.value = false
    }
  }

  return {
    // state
    settings,
    error,
    fieldErrors,
    isLoading,
    isUpdatingAppSetting,
    isUpdatingLogo,
    isUpdatingFavicon,

    // actions
    fetchSettings,
    updateAppSetting,
    updateLogo,
    updateFavicon,
  }
})
