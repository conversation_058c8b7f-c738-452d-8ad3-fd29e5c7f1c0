import { processErrors } from '@/lib'
import { apiClient } from '@tenant/composables'
import type { ApiResponse } from '@/types/api'
import type { User } from '@tenant/types'
import { defineStore } from 'pinia'
import { ref } from 'vue'
import { toast } from 'vue-sonner'
import { z } from 'zod'
import { useAuthStore, useGlobalSettingsStore } from '@tenant/stores'

export const createPasswordSchema = z
  .object({
    token: z.string().min(1, 'Token is required'),
    password: z.string().min(8, 'New Password must be at least 8 characters long'),
    confirm_password: z.string().min(8, 'Confirm Password must be at least 8 characters long'),
  })
  .refine((data) => data.password === data.confirm_password, {
    message: 'Passwords do not match',
  })

type CreatePasswordResponse = ApiResponse & {
  isAuth?: 0 | 1
  userData?: User
}

export const onboardingAppSettingSchema = z.object({
  app_name: z.string().min(1, 'Pharmacy name is required'),
  app_logo: z.string().optional(),
  app_favicon: z.string().optional(),
})

export const useOnboardingStore = defineStore('onboardingStore', () => {
  const authStore = useAuthStore()
  const globalSettingsStore = useGlobalSettingsStore()

  const isCreatingPassword = ref(false)
  const error = ref<string | null>(null)
  const fieldErrors = ref<Record<string, string[]>>({})

  async function createPassword(payload: z.infer<typeof createPasswordSchema>) {
    isCreatingPassword.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<CreatePasswordResponse>(
        '/admin/setup/account/password',
        payload,
      )

      if (response.data.status === 200) {
        if (response.data.userData) {
          authStore.setUser(response.data.userData)
        }
        toast.success(response.data.message || 'Password created successfully')
        return { success: true }
      } else if (response.data.isAuth === 1) {
        toast.info(
          response.data.message ||
            'You have already created your password. Please login to access the admin panel.',
        )
        return { success: false, redirectToLogin: true }
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return { success: false }
      } else {
        error.value = response.data.message || 'Failed to create new password'
        return { success: false }
      }
    } catch (err: any) {
      console.error('Error creating new password:', err)
      error.value = processErrors(err, 'An error occurred while creating new password.')
      return { success: false }
    } finally {
      isCreatingPassword.value = false
    }
  }

  const isUpdatingAppConfig = ref(false)

  async function updateAppConfig(payload: z.infer<typeof onboardingAppSettingSchema>) {
    isUpdatingAppConfig.value = true
    error.value = null
    fieldErrors.value = {}

    try {
      const response = await apiClient.post<CreatePasswordResponse>(
        '/admin/update-onboarding-app-configs',
        payload,
      )

      if (response.data.status === 200) {
        toast.success(response.data.message || 'Pharmacy config updated successfully')
        await globalSettingsStore.loadSettings()
        return { success: true }
      } else if (response.data.errors) {
        fieldErrors.value = response.data.errors
        return { success: false }
      } else {
        error.value = response.data.message || 'Failed to update pharmacy config'
        return { success: false }
      }
    } catch (err: any) {
      console.error('Error updating pharmacy config:', err)
      error.value = processErrors(err, 'An error occurred while updating pharmacy config.')
      return { success: false }
    } finally {
      isUpdatingAppConfig.value = false
    }
  }

  return {
    isCreatingPassword,
    error,
    fieldErrors,
    createPassword,

    isUpdatingAppConfig,
    updateAppConfig,
  }
})
