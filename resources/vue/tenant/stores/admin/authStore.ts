import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import type { User } from '@tenant/types'
import { apiClient } from '@tenant/composables'
import router from '@tenant/router'

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(
    localStorage.getItem('current_user')
      ? JSON.parse(localStorage.getItem('current_user') as string)
      : null,
  )
  const isVerifying = ref(false)
  const isAuthenticated = computed(() => user.value !== null)

  function setUser(userData: User) {
    user.value = userData
    localStorage.setItem('current_user', JSON.stringify(userData))
  }

  function clearAuth() {
    user.value = null
    localStorage.removeItem('current_user')
  }

  async function verifyToken(): Promise<boolean> {
    if (isVerifying.value) return false

    try {
      isVerifying.value = true
      const { data } = await apiClient.get('/admin/verify-token')

      if (data.status === 200) {
        return true
      } else {
        throw data
      }
    } catch (error) {
      console.error('Token verification failed:', error)
      clearAuth()
      return false
    } finally {
      isVerifying.value = false
    }
  }

  async function logout() {
    try {
      await apiClient.get('/admin/logout')
    } catch (error) {
      console.error('Logout failed:', error)
    } finally {
      clearAuth()
      router.push({ name: 'admin-login' })
    }
  }

  return {
    user,
    isVerifying,
    isAuthenticated,
    setUser,
    clearAuth,
    logout,
    verifyToken,
  }
})
