import { useGlobalSettingsStore } from '@tenant/stores'
import type { Middleware } from './types'

export const fetchCategories: Middleware = async ({ next }) => {
  const globalSettingsStore = useGlobalSettingsStore()

  await globalSettingsStore.fetchLandingPageData()

  return next()
}

export const fetchHeaderData: Middleware = async ({ next }) => {
  const globalSettingsStore = useGlobalSettingsStore()

  await globalSettingsStore.fetchHeaderData()

  return next()
}
