import { useAuthStore } from '@tenant/stores'
import type { Middleware } from './types'
import { apiClient } from '@tenant/composables'
import type { ApiResponse } from '@/types/api'
import { toast } from 'vue-sonner'

export const auth: Middleware = async ({ to, next }) => {
  const authStore = useAuthStore()

  const isValid = await authStore.verifyToken()

  if (!isValid) {
    return next({
      name: 'admin-login',
      query: { redirect: to.fullPath },
    })
  }

  return next()
}

export const guest: Middleware = async ({ next }) => {
  const authStore = useAuthStore()

  if (authStore.user) {
    return next({ name: 'admin-dashboard' })
  }

  return next()
}

export const validateOnboardingLink: Middleware = async ({ to, next }) => {
  const token = to.params.token as string

  const { data } = await apiClient.get<ApiResponse & { isAuth?: 0 | 1; linkExpired?: boolean }>(
    `/admin/verify/account/setup/${token}`,
  )

  if (data.status === 200) {
    // link valid
    next()
  } else if (data.linkExpired === true) {
    // link expired
    next({ name: 'admin-onboard-link-expired' })
  } else if (data.isAuth === 1) {
    // link already used
    toast.info(
      data.message ||
        'You have already created your password. Please login to access the admin panel.',
    )
    next({ name: 'admin-login' })
  } else {
    // link invalid
    next({ name: 'not-found' })
  }
}
