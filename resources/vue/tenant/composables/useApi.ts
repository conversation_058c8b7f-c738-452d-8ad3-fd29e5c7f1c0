import axios from 'axios'
import type { AxiosInstance, AxiosResponse, AxiosError } from 'axios'
import { useAuthStore } from '@tenant/stores'
import router from '@tenant/router'

const apiClient: AxiosInstance = axios.create({
  baseURL: window.location.origin + '/api',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds
  withCredentials: true,
})

// Request interceptor
// apiClient.interceptors.request.use(
//   (config) => {
//     const authStore = useAuthStore()

//     if (authStore.token) {
//       config.headers.Authorization = `Bearer ${authStore.token}`
//     }

//     return config
//   },
//   (error) => {
//     return Promise.reject(error)
//   },
// )

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Handle 401 unauthorized responses
    if (response?.data?.status === 401) {
      const authStore = useAuthStore()
      authStore.clearAuth()
      router.push({ name: 'admin-login' })
    }

    // Handle successful responses
    return response
  },
  (error: AxiosError) => {
    // Handle error responses
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.clearAuth()
      router.push({ name: 'admin-login' })
    }

    return Promise.reject(error)
  },
)

export { apiClient }
