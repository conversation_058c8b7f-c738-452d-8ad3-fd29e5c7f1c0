import type { LucideIcon } from 'lucide-vue-next'
import { LayoutList, LayoutDashboard, Package, Settings2, Star, BookOpen } from 'lucide-vue-next'

export interface SubMenuItem {
  title: string
  routeName: string
}

export interface MenuItem {
  title: string
  routeName?: string
  icon?: LucideIcon
  isActive?: boolean
  items?: SubMenuItem[]
}

export interface MenuGroup {
  label?: string
  items: MenuItem[]
}

export type MenuConfig = MenuGroup[]

export const menuItems: MenuConfig = [
  {
    items: [
      {
        title: 'Dashboard',
        routeName: 'admin-dashboard',
        icon: LayoutDashboard,
        isActive: true,
      },
    ],
  },
  {
    label: 'Catalog',
    items: [
      {
        title: 'Categories',
        icon: LayoutList,
        routeName: 'admin-categories',
      },
      {
        title: 'Sub-Categories',
        icon: LayoutList,
        routeName: 'admin-sub-categories',
      },
      {
        title: 'Products',
        icon: Package,
        routeName: 'admin-products',
      },
      {
        title: 'Top Products',
        icon: Star,
        routeName: 'admin-top-products',
      },
    ],
  },
  {
    label: 'Pharmacy',
    items: [
      {
        title: 'Settings',
        icon: Settings2,
        routeName: 'admin-settings',
      },
      {
        title: 'How to Guide',
        icon: BookOpen,
        routeName: 'admin-guide',
      },
    ],
  },
]
