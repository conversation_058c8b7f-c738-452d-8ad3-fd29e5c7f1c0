/**
 * Format a date to a human-readable string.
 *
 * @param date - The date to format.
 * @param locale - The locale to use for formatting. Defaults to 'en-US'.
 * @returns A human-readable string representing the date.
 */
export function formatDate(date: Date | string, locale = 'en-US'): string {
  return new Date(date).toLocaleDateString(locale)
}

/**
 * Calculate a string representing the time elapsed since a given date.
 *
 * @param date - The date to calculate the time elapsed since.
 * @returns A string representing the time elapsed since the given date.
 */
export function timeAgo(date: Date | string): string {
  const diff = (new Date().getTime() - new Date(date).getTime()) / 1000
  if (diff < 60) return 'just now'
  if (diff < 3600) return `${Math.floor(diff / 60)} min ago`
  if (diff < 86400) return `${Math.floor(diff / 3600)} hours ago`
  return `${Math.floor(diff / 86400)} days ago`
}

/**
 * Get the user's timezone.
 *
 * @returns The user's timezone.
 */
export function getUserTimezone() {
  // Try modern method first
  if (typeof Intl !== 'undefined' && Intl.DateTimeFormat) {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone
    } catch (e) {
      console.warn('Intl API failed, falling back to offset method')
    }
  }

  // Fallback to offset method
  const offset = new Date().getTimezoneOffset()
  const hours = Math.abs(offset / 60)
  const minutes = Math.abs(offset % 60)
  const sign = offset > 0 ? '-' : '+'

  return `UTC${sign}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}`
}
