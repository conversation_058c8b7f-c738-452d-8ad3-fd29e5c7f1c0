name: Backend Deploy

on:
  push:
    branches: [main]
    paths:
      - '**'
      - '.github/workflows/backend-deploy.yml'
  workflow_dispatch:

permissions:
  contents: read
  id-token: write
  actions: read

env:
  PHP_VERSION: '8.4'
  COMPOSER_CACHE_DIR: ~/.composer/cache

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 20
    concurrency:
      group: deploy-production
      cancel-in-progress: false

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Setup Node.js
        uses: actions/setup-node@v5
        with:
          node-version: '24'

      - name: Validate composer files
        run: |
          if [[ ! -f "composer.json" ]]; then
            echo "::error::composer.json not found"
            exit 1
          fi
          if [[ ! -f "composer.lock" ]]; then
            echo "::warning::composer.lock not found"
          fi
          composer validate --strict --no-check-all

      - name: Setup PHP with caching
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ env.PHP_VERSION }}
          tools: composer:v2
          coverage: none
          extensions: mbstring, xml, ctype, json, curl, zip, openssl, pdo, tokenizer, bcmath
          ini-values: memory_limit=1G, max_execution_time=300

      - name: Get Composer cache directory
        id: composer-cache
        run: echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Cache Composer dependencies
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: composer-${{ runner.os }}-php${{ env.PHP_VERSION }}-${{ hashFiles('composer.lock') }}
          restore-keys: |
            composer-${{ runner.os }}-php${{ env.PHP_VERSION }}-
            composer-${{ runner.os }}-

      - name: Install Composer dependencies
        run: |
          composer install \
            --no-interaction \
            --prefer-dist \
            --optimize-autoloader \
            --no-dev \
            --no-progress \
            --classmap-authoritative

      - name: Install Vapor CLI globally
        run: |
          composer global require laravel/vapor-cli --no-progress --no-interaction
          echo "$HOME/.composer/vendor/bin" >> $GITHUB_PATH

      - name: Verify Vapor CLI installation
        run: |
          if ! command -v vapor &> /dev/null; then
            echo "::error::Vapor CLI installation failed"
            exit 1
          fi
          vapor --version

      - name: Security audit
        run: composer audit --no-dev
        continue-on-error: true

      - name: Validate Vapor configuration
        run: |
          if [[ ! -f "vapor.yml" ]]; then
            echo "::error::vapor.yml not found"
            exit 1
          fi

      - name: Deploy to production
        id: deploy
        run: |
          COMMIT_MSG="${{ github.event.head_commit.message }}"
          if [[ -z "$COMMIT_MSG" ]]; then
            COMMIT_MSG="Manual deployment from main (${{ github.sha }})"
          fi
          set -e
          vapor deploy production \
            --commit="${{ github.sha }}" \
            --message="${COMMIT_MSG}" \
            --without-waiting
        env:
          VAPOR_API_TOKEN: ${{ secrets.VAPOR_API_TOKEN }}
          VITE_APP_NAME: ${{ vars.VITE_APP_NAME }}
          VITE_SITE_LOGO: ${{ vars.VITE_SITE_LOGO }}
          VITE_RECAPTCHA_SITE_KEY: ${{ vars.VITE_RECAPTCHA_SITE_KEY }}
          VITE_COMPANY_SUPPORT_EMAIL: ${{ vars.VITE_COMPANY_SUPPORT_EMAIL }}
          VITE_TENANCY_CENTRAL_DOMAINS: ${{ vars.VITE_TENANCY_CENTRAL_DOMAINS }}

      - name: Post-deployment validation
        if: success()
        run: echo "Deployment completed successfully!"

      - name: Deployment summary
        if: always()
        run: |
          echo "### 🚀 Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "- **Status**: ${{ job.status == 'success' && '✅ Success' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Environment**: production" >> $GITHUB_STEP_SUMMARY
          echo "- **Branch**: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit**: \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Triggered by**: ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          if [[ "${{ github.event.head_commit.message }}" != "" ]]; then
            echo "- **Message**: ${{ github.event.head_commit.message }}" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Notify on failure
        if: failure()
        run: |
          echo "::error::Deployment to production failed"
          echo "::error::Check the logs above for details"
