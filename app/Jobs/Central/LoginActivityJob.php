<?php

namespace App\Jobs\Central;

use App\Models\UserAuthActivityLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\DB;

class LoginActivityJob implements ShouldQueue
{
    use Queueable;

    public $user_details;

    /**
     * Create a new job instance.
     */
    public function __construct($user_details)
    {
        $this->user_details = $user_details;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $user_details = $this->user_details;
        $agent = $user_details['agent'];
        $ip = $user_details['ip'];
        $result = UserAuthActivityLog::log($user_details['user_id'], 1, 'login',$agent,$ip);
        DB::table('personal_access_tokens')
                    ->where('id', $user_details['token_id'])
                    ->update(['user_timezone'=>$result['timezone']]);

    }
}
