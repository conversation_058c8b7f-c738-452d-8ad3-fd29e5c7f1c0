<?php

namespace App\Jobs\Central;

use App\Constants\MailgunEmailTemplate;
use App\Services\MailgunServices;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class SendInvitation<PERSON>ailJob implements ShouldQueue
{
    use Queueable;

    public $email_data;

    /**
     * Create a new job instance.
     */
    public function __construct($email_data)
    {
        $this->email_data = $email_data;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $email_data = $this->email_data;
        $template = MailgunEmailTemplate::$INVITATION_EMAIL;
        if($template){
            $pharmacy_name = ucwords($email_data['pharmacy_name']);
            $company_name = config('services.company.name');
            $email_data = [
                "template_name" => $template,
                "template_values" => [
                    "company_logo_url" => config('services.company.email_logo_url'),
                    "company_name" => config('services.company.name'),
                    "pharmacy_admin_name" => ucwords($email_data['first_name']),
                    "pharmacy_name" => $pharmacy_name,
                    "pharmacy_admin_email" => $email_data['email'],
                    "button_action_url" => $email_data['button_action_url'],
                    "company_support_email" => config('services.company.support_email'),
                    "company_support_phone_no" => config('services.company.support_phone_number'),
                    "company_address" => config('services.company.address')
                ],
                //"from_email" => "",
                "to_email" => $email_data['email'],
                "subject" => "Welcome to $company_name – Set Up Your Pharmacy Account"
            ];
            MailgunServices::MailSend($email_data);
        } else {
            \Log::error('Invitation mail template not found.');
        }
    }
}
