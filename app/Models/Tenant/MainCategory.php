<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class MainCategory extends Model
{
    use HasFactory,HasUuids;

    protected $guarded = ['id'];

    public function getImageAttribute($image){
        if(empty($image)){
            return null;
        } else {
            return \App\Traits\FileUploadTrait::GetFilePath($image,'main_category');
        }
    }

    public function sub_categories(){
        return $this->hasMany(SubCategory::class, 'main_category_id', 'id');
    }

    public function products(){
        return $this->hasMany(Product::class, 'main_category_id');
    }
}
