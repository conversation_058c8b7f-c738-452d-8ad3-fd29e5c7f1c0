<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AppSetting extends Model
{
    use HasFactory;
    protected $guarded = ['id'];

    protected $hidden = ['created_at', 'updated_at'];

    public function getAppLogoAttribute($app_logo){
        if(empty($app_logo)){
            return null;
        } else {
            return \App\Traits\FileUploadTrait::GetFilePath($app_logo,'app_logo');
        }
    }

    public function getAppFaviconAttribute($app_favicon){
        if(empty($app_favicon)){
            return null;
        } else {
            return \App\Traits\FileUploadTrait::GetFilePath($app_favicon,'app_logo');
        }
    }

    public function getLpDataAttribute($lp_data){
        return ($lp_data) ? json_decode($lp_data,true) : null;
    }

    public function getImages($image){
        if(empty($image)){
            return null;
        } else if (str_starts_with($image, 'http')) {
            return $image;
        } else {
            return \App\Traits\FileUploadTrait::GetFilePath($image,'frontend_images');
        }
    }
}
