<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class Product extends Model
{
    use HasFactory,HasUuids;
    protected $guarded = ['id'];

    public function getImageAttribute($image){
        if(empty($image)){
            return null;
        } else {
            return \App\Traits\FileUploadTrait::GetFilePath($image,'products');
        }
    }

    public function main_category(){
        return $this->belongsTo(MainCategory::class, 'main_category_id');
    }

    public function sub_category(){
        return $this->belongsTo(SubCategory::class, 'sub_category_id');
    }

    public function product_item(){
        return $this->hasOne(ProductItem::class, 'product_id');
    }

    public function product_items(){
        return $this->hasMany(ProductItem::class, 'product_id');
    }

    public function product_descriptions(){
        return $this->hasMany(ProductDescription::class, 'product_id');
    }

    public function product_faqs(){
        return $this->hasMany(ProductFaq::class, 'product_id');
    }
}
