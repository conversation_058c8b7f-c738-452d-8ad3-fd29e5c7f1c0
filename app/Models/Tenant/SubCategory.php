<?php

namespace App\Models\Tenant;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class SubCategory extends Model
{
    use HasFactory,HasUuids;
    protected $guarded = ['id'];

    public function main_category(){
        return $this->belongsTo(MainCategory::class, 'main_category_id');
    }

    public function products(){
        return $this->hasMany(Product::class, 'sub_category_id');
    }
}
