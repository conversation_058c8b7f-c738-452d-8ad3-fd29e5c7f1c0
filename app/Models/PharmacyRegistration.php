<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PharmacyRegistration extends Model
{
    use HasFactory,HasUuids;

    protected $guarded = ['id'];

    public function payment_logs(){
        return $this->hasMany(PaymentLog::class);
    }

    public function getAddressDetailsAttribute($address_details){
        return ($address_details) ? json_decode($address_details) : null;
    }
}
