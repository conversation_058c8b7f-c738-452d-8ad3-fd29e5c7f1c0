<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Carbon\Carbon;

class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains;

    protected $fillable = ['id', 'name', 'email','pharmacy_name', 'first_name', 'last_name','is_invitation_accepted', 'phone_number', 'status', 'data','invitation_token','invitation_expired_at'];

    public static function getCustomColumns(): array
    {
        return ['id', 'name', 'email','pharmacy_name', 'first_name', 'last_name','is_invitation_accepted', 'phone_number', 'status','invitation_token','invitation_expired_at'];
    }

    protected function phoneNumber(): Attribute
    {
        return Attribute::make(
            get: fn ($value) => $this->formatPhoneNumber($value),
            set: fn ($value) => preg_replace('/\D/', '', $value) // Store only digits
        );
    }

    /**
     * Format the phone number into (XXX) XXX-XXXX format.
     */
    private function formatPhoneNumber(?string $phone_number): ?string
    {
        $phone_number = preg_replace('/\D/', '', $phone_number); // Remove non-numeric characters

        return (strlen($phone_number) === 10)
            ? sprintf('(%s) %s-%s', substr($phone_number, 0, 3), substr($phone_number, 3, 3), substr($phone_number, 6))
            : $phone_number;
    }

    public function getSubDomainLinkAttribute(){
        $centralDomain = config('tenancy.central_domains')[0];
        if(env('APP_ENV') === 'local') {
            return 'http://'.$this->id.'.'.$centralDomain;
        } else {
            return 'https://'.$this->id.'.'.$centralDomain;
        }
    }

    public function getSubDomainNameAttribute(){
        $centralDomain = config('tenancy.central_domains')[0];
        if(env('APP_ENV') === 'local') {
            return $this->id.'.'.$centralDomain;
        } else {
            return $this->id.'.'.$centralDomain;
        }
    }

    public function setDataAttribute(){
        $this->attributes['data'] = null;
    }
}
