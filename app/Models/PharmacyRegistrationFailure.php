<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PharmacyRegistrationFailure extends Model
{
    use HasFactory,HasUuids;

    protected $guarded = ['id'];

    public function getAddressDetailsAttribute($address_details){
        return ($address_details) ? json_decode($address_details) : null;
    }

    public function getPaymentCardDetailsAttribute($payment_card_details){
        return ($payment_card_details) ? json_decode($payment_card_details) : null;
    }
}
