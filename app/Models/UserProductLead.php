<?php

namespace App\Models;

use App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class UserProductLead extends Model
{
    use HasFactory,HasUuids;

    protected $guarded = ['id'];


    public function pharmacy_details(){
        return $this->belongsTo(Tenant::class,'tenant_id');
    }

    public function getProductDetailsAttribute($product_details){
        return ($product_details) ? json_decode($product_details,true) : null;
    }

    public function getImage($image,$tenant_id){
        if(empty($image)){
            return null;
        } else {
            $base_path = config('tenancy.filesystem.suffix_base')."{$tenant_id}/".$image;
            return \App\Traits\FileUploadTrait::GetProductImagePath($base_path,'products');
        }
    }
}
