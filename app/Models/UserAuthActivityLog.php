<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Jenssegers\Agent\Agent;
use App\Models\Tenant\User;
use App\Services\LocationService;

class UserAuthActivityLog extends Model
{
    use HasFactory,HasUuids;

    protected $guarded = ['id'];

    public static function log($userId, $logStatus=null,$type,$agent=null,$ip=null,$tenant=null)
    {
        $data['timezone'] = config('app.timezone');
        $data['ip_address'] = null;
        $data['location'] = null;
        $user = User::select('id')->where('id', $userId)->first();
        if($user){
            $timezone_detail = LocationService::get_ip_address_details($ip);
            $data['timezone'] = (!empty($timezone_detail) && @$timezone_detail['timezone']) ? $timezone_detail['timezone'] : config('app.timezone');
            $data['ip_address'] = (!empty($timezone_detail) && @$timezone_detail['ip_address']) ? $timezone_detail['ip_address'] : null;
            $data['location'] = (!empty($timezone_detail) && @$timezone_detail['location']) ? $timezone_detail['location'] : null;
            try {
                if($agent==null){
                    $agent = new Agent();
                }

                UserAuthActivityLog::create([
                    'user_id' => $userId,
                    'status' => $logStatus,
                    'ip_address' => $data['ip_address'],
                    'timezone' => $data['timezone'],
                    'location' => $data['location'],
                    'browser' => $agent->browser() ?? null,
                    'platform' => $agent->platform() ?? null,
                    'device_type' => $agent->deviceType() ?? null,
                    'user_agent' => $agent->getUserAgent() ?? null,
                ]);
                return $data;
            } catch (\Exception $e) {
                return $data;
            }
        }
        return $data;
    }
}
