<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Concerns\HasUuids;

class PlanSetting extends Model
{
    use HasFactory,HasUuids;

    protected $guarded = ['id'];

    public function getImageAttribute($image){
        if(empty($image)){
            return null;
        } else {
            return \App\Traits\FileUploadTrait::GetFilePath($image,'plans');
        }
    }

    public function getFeaturesAttribute($features){
        return ($features) ? json_decode($features) : [];
    }
}
