<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidateImageFile implements Rule
{
    protected $message;

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed   $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (empty($value)) {
            $this->message = trans('admin/errors.image_required');
            return false;
        }

        $ext = explode(';base64,', $value);
        // Validate mime type
        $mime = explode(':', $ext[0]);
        if (count($mime) < 2 || strpos($mime[1], 'image/') !== 0) {
            $this->message = trans('admin/errors.not_image_file');
            return false;
        }

        // Split base64 string
        if (count($ext) <= 1) {
            $this->message = trans('admin/errors.invalid_base64_format');
            return false;
        }

        // Decode image
        $decodedImage = base64_decode($ext[1], true);
        if (!$decodedImage) {
            $this->message = trans('admin/errors.invalid_image_data');
            return false;
        }

        // Validate size (3 MB = 3072 KB)
        $imageSize = strlen($decodedImage) / 1024;
        if ($imageSize > 3072) {
            $this->message = trans('admin/errors.image_too_large');
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return $this->message ?? trans('admin/errors.should_image_file');
    }
}
