<?php

namespace App\Rules;

use Closure;
use Illuminate\Support\Facades\Http;
use Illuminate\Contracts\Validation\ValidationRule;

class ReCaptcha implements ValidationRule
{
    /**
     * Run the validation rule.
     *
     * @param  \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString  $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if(env('APP_ENV')!='local'){
            $response = Http::get("https://www.google.com/recaptcha/api/siteverify", [
                'secret' => config('services.google_recaptcha.secret_key'),
                'response' => $value
            ]);
            if($response->json()["success"]==false){
                $fail('Captcha failed. Please try again.');
            }
        }
    }
}
