<?php

namespace App\Http\Requests\API\Tenant\Admin\Product;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class EditProductRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function rules()
    {
        return [
            "id" => "required",
            'product_type' => ['required', Rule::in(['single','programs'])],
            // For Single Product
            'product_name' => [
                'required_if:product_type,single',
                'string',
                'max:255'
            ],
            'product_qty' => [
                'nullable',
                'string',
                'max:255'
            ],
            'product_form' => [
                'nullable',
                'string',
                'max:255'
            ],
            'product_strength' => [
                'nullable',
                'string',
                'max:255'
            ],
            'price' => [
                'required_if:product_type,single',
                'numeric',
                'min:0.1'
            ],
            'xpedicare_url' => [
                'required_if:product_type,single',
                'string'
            ],
            // For Programs
            'product_title' => [
                'required_if:product_type,programs',
                'string',
                'max:255'
            ],
            'product_items' => [
                'required_if:product_type,programs',
                'array',
                'min:1'
            ],
            'product_items.*.product_name' => [
                'required_if:product_type,programs',
                'string',
                'max:255'
            ],
            'product_items.*.product_qty' => [
                'nullable',
                'string',
                'max:255'
            ],
            'product_items.*.product_form' => [
                'nullable',
                'string',
                'max:255'
            ],
            'product_items.*.product_strength' => [
                'nullable',
                'string',
                'max:255'
            ],
            'product_items.*.price' => [
                'required_if:product_type,programs',
                'numeric',
                'min:0.1'
            ],
            'product_items.*.xpedicare_url' => [
                'required_if:product_type,programs',
                'string'
            ],
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $items = $this->product_items ?? [];
            $combination = [];

            foreach ($items as $index => $item) {
                $key = strtolower($item['product_name']);
                if (in_array($key, $combination)) {
                    $validator->errors()->add("product_items.$index.product_name", "The product name should be unique.");
                }
                $combination[] = $key;
            }
        });
    }

    public function messages()
    {
        return [
            "main_category_id.required" => "The main category field is required.",
            "main_category_id.exists" => "The selected main category is invalid or does not exist.",
            "sub_category_id.required" => "The sub category field is required.",
            "sub_category_id.exists" => "The selected sub category is invalid or does not exist.",

            'product_type.required' => 'The product type field is required.',
            'product_type.in' => 'The product type must be either single or programs.',

            'product_title.required_if' => 'The product title is required.',
            'product_items.required_if' => 'The product items are required.',

            'product_name.required_if' => 'The product name is required.',
            'product_qty.required_if' => 'The product quantity is required.',
            'product_form.required_if' => 'The product form is required.',
            'product_strength.required_if' => 'The product strength is required.',
            'price.required_if' => 'The price is required.',
            'xpedicare_url.required_if' => 'The Xpedicare link is required.',

            'product_items.*.product_name.required_if' => 'The product name is required.',
            'product_items.*.product_qty.required_if' => 'The product quantity is required.',
            'product_items.*.product_form.required_if' => 'The product form is required.',
            'product_items.*.product_strength.required_if' => 'The product strength is required.',
            'product_items.*.price.required_if' => 'The price is required.',
            'product_items.*.xpedicare_url.required_if' => 'The Xpedicare link is required.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
