<?php

namespace App\Http\Requests\API\Tenant\Admin\Product;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateProductDescriptionFaqRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'id' => ['required'],
                'product_descriptions' => 'array',
                'product_descriptions.*.title' => 'nullable|string|required_with:product_descriptions.*.description',
                'product_descriptions.*.description' => 'nullable|string|required_with:product_descriptions.*.title',

                'product_faqs' => 'array',
                'product_faqs.*.question' => 'nullable|string|required_with:product_faqs.*.answer',
                'product_faqs.*.answer' => 'nullable|string|required_with:product_faqs.*.question',
        ];
    }

    public function messages(): array
    {
        return [
            'product_descriptions.*.title.required_with' => 'The title field is required.',
            'product_descriptions.*.description.required_with' => 'The description field is required.',
            'product_faqs.*.question.required_with' => 'The question field is required.',
            'product_faqs.*.answer.required_with' => 'The answer field is required.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
