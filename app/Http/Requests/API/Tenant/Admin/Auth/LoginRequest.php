<?php

namespace App\Http\Requests\API\Tenant\Admin\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use App\Rules\ReCaptcha;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => ['required','email'],
            'password' => ['required', 'string'],
            // 'g-recaptcha-response' => (env('APP_ENV')=='local') ? ['nullable'] : ['required']
        ];
    }

    public function messages(): array
    {
        return [
            'g-recaptcha-response.required' => trans('errors.captcha_required')
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
