<?php

namespace App\Http\Requests\API\Tenant\Admin\SubCategory;

use App\Rules\ValidateImageFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class AddSubCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'main_category_id' => ['required',Rule::exists('main_categories','id')],
            'name' => ['required','max:255',Rule::unique('sub_categories', 'name')->where( 'main_category_id',$this['main_category_id'])],
        ];
    }

    public function messages()
    {
        return [
            "main_category_id.required" => "The main category field is required.",
            "main_category_id.exists" => "The selected main category is invalid or does not exist.",
            'name.unique' => "The subcategory name has already been taken for this main category."
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
