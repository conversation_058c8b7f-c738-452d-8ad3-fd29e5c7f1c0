<?php

namespace App\Http\Requests\API\Tenant\Admin\SubCategory;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class EditSubCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'main_category_id' => ['required',Rule::exists('main_categories','id')],
            'name' => ['required','max:100',Rule::unique('sub_categories', 'name')->where( 'main_category_id',$this['main_category_id'])->ignore($this['id'])]
        ];
    }

    public function messages()
    {
        return [
            'name.unique' => "The category name has already been taken."
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
