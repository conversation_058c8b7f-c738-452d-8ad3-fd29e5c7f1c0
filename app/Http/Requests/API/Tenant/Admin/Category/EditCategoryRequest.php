<?php

namespace App\Http\Requests\API\Tenant\Admin\Category;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class EditCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required','max:100',Rule::unique('main_categories')->where('gender',$this->gender)->ignore($this->id)],
            "gender" => ['nullable','in:Male,Female'],
            'description' => ['nullable','max:100']
        ];
    }

    public function messages()
    {
        return [
            'name.unique' => "The category name has already been taken."
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
