<?php

namespace App\Http\Requests\API\Tenant\Admin\Category;

use App\Rules\ValidateImageFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class AddCategoryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => ['required','max:255',Rule::unique('main_categories')->where('gender',$this->gender)],
            "gender" => ['nullable','in:Male,Female'],
            'image' => ['required',new ValidateImageFile()],
            'description' => ['nullable','max:100']
        ];
    }

    public function messages()
    {
        return [
            'name.unique' => "The category name has already been taken."
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
