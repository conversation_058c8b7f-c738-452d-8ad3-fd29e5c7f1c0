<?php

namespace App\Http\Requests\API\Tenant\Admin\AppSetting;

use App\Rules\ValidateImageFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class UpdateSocialUrlRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'privacy_policy_url' => ['nullable','max:255'],
            'terms_and_conditions_url' => ['nullable','max:255'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
