<?php

namespace App\Http\Requests\API\Tenant\Admin\AppSetting;

use App\Rules\ValidateImageFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class LandingPageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            // 'hero' => 'required|array',
            // 'hero.phrases' => 'required|array|min:1',
            // 'hero.phrases.*' => 'required|string',
            // 'hero.title' => 'required|string|max:255',
            // 'hero.subtitle' => 'required|string|max:500',
            // 'hero.features' => 'required|array|min:1',
            // 'hero.features.*.text' => 'required|string|max:255',

            // 'heroCallout' => 'required|array',
            // 'heroCallout.title' => 'required|string|max:255',
            // 'heroCallout.buttonText' => 'required|string|max:100',
            // 'heroCallout.backgroundImage' => 'required|string|max:255',
            // 'heroCallout.buttonLink' => 'required|string|max:255',
            // 'heroCallout.newBackgroundImage' => ['nullable', new ValidateImageFile()],

            // 'features' => 'required|array',
            // 'features.title' => 'required|string|max:255',
            // 'features.subtitle' => 'required|string|max:500',
            // 'features.featureList' => 'required|array|min:1',
            // 'features.featureList.*.title' => 'required|string|max:255',
            // 'features.featureList.*.description' => 'required|string|max:500',
            // 'features.featureList.*.icon' => 'required|string',

            // 'productHighlights' => 'required|array',
            // 'productHighlights.title' => 'required|string|max:255',
            // 'productHighlights.buttonText' => 'required|string|max:100',
            // 'productHighlights.buttonLink' => 'required|string|max:255',

            // 'testimonials' => 'required|array',
            // 'testimonials.title' => 'required|string|max:255',
            // 'testimonials.subtitle' => 'required|string|max:500',
            // 'testimonials.testimonialList' => 'required|array|min:1',
            // 'testimonials.testimonialList.*.name' => 'required|string|max:255',
            // 'testimonials.testimonialList.*.review' => 'required|string|max:1000',

            // 'fixItTogether' => 'required|array',
            // 'fixItTogether.title' => 'required|string|max:255',
            // 'fixItTogether.cards' => 'required|array|min:1',
            // 'fixItTogether.cards.*.tag' => 'required|string|max:100',
            // 'fixItTogether.cards.*.title' => 'required|string|max:255',
            // 'fixItTogether.cards.*.image' => 'required|string|max:255',
            // 'fixItTogether.cards.*.link' => 'required|string|max:255',
            // 'fixItTogether.cards.*.newBackgroundImage' => ['nullable',new ValidateImageFile()],

            // 'howItWorks' => 'required|array',
            // 'howItWorks.title' => 'required|string|max:255',
            // 'howItWorks.subtitle' => 'required|string|max:500',
            // 'howItWorks.steps' => 'required|array|min:1',
            // 'howItWorks.steps.*.step' => 'required|string|max:10',
            // 'howItWorks.steps.*.title' => 'required|string|max:255',
            // 'howItWorks.steps.*.description' => 'required|string|max:500',
            // 'howItWorks.steps.*.icon' => 'required|string',
            // 'howItWorks.steps.*.color' => 'required|string|max:50',
            // 'howItWorks.steps.*.iconBg' => 'required|string|max:50',
            // 'howItWorks.steps.*.iconColor' => 'required|string|max:50',
            // 'howItWorks.steps.*.stepColor' => 'required|string|max:50',
            // 'howItWorks.buttonText' => 'required|string|max:100',
            // 'howItWorks.buttonLink' => 'required|string|max:255',

            // 'faq' => 'required|array',
            // 'faq.title' => 'required|string|max:255',
            // 'faq.subtitle' => 'required|string|max:500',
            // 'faq.faqList' => 'required|array|min:1',
            // 'faq.faqList.*.question' => 'required|string|max:500',
            // 'faq.faqList.*.answer' => 'required|string|max:2000',

            // 'cta' => 'required|array',
            // 'cta.title' => 'required|string|max:255',
            // 'cta.subtitle' => 'required|string|max:500',
            // 'cta.paragraph' => 'required|string|max:2000',
            // 'cta.buttonText' => 'required|string|max:100',
            // 'cta.buttonLink' => 'required|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            // Hero
            'hero.required' => 'Hero section is required.',
            'hero.phrases.required' => 'At least one hero phrase is required.',
            'hero.phrases.*.required' => 'Each hero phrase is required.',
            'hero.title.required' => 'Hero title is required.',
            'hero.subtitle.required' => 'Hero subtitle is required.',
            'hero.features.required' => 'At least one hero feature is required.',
            'hero.features.*.text.required' => 'Each hero feature text is required.',

            // Hero Callout
            'heroCallout.required' => 'Hero callout section is required.',
            'heroCallout.title.required' => 'Hero callout title is required.',
            'heroCallout.buttonText.required' => 'Hero callout button text is required.',
            'heroCallout.backgroundImage.required' => 'Hero callout background image is required.',
            'heroCallout.buttonLink.required' => 'Hero callout button link is required.',

            // Features
            'features.required' => 'Features section is required.',
            'features.title.required' => 'Features title is required.',
            'features.subtitle.required' => 'Features subtitle is required.',
            'features.featureList.required' => 'At least one feature item is required.',
            'features.featureList.*.title.required' => 'Each feature item must have a title.',
            'features.featureList.*.description.required' => 'Each feature item must have a description.',
            'features.featureList.*.icon.required' => 'Each feature item must have an icon.',

            // Product Highlights
            'productHighlights.required' => 'Product highlights section is required.',
            'productHighlights.title.required' => 'Product highlights title is required.',
            'productHighlights.buttonText.required' => 'Product highlights button text is required.',
            'productHighlights.buttonLink.required' => 'Product highlights button link is required.',

            // Testimonials
            'testimonials.required' => 'Testimonials section is required.',
            'testimonials.title.required' => 'Testimonials title is required.',
            'testimonials.subtitle.required' => 'Testimonials subtitle is required.',
            'testimonials.testimonialList.required' => 'At least one testimonial is required.',
            'testimonials.testimonialList.*.name.required' => 'Each testimonial must include a name.',
            'testimonials.testimonialList.*.review.required' => 'Each testimonial must include a review.',

            // Fix It Together
            'fixItTogether.required' => 'Fix it together section is required.',
            'fixItTogether.title.required' => 'Fix it together title is required.',
            'fixItTogether.cards.required' => 'At least one fix it together card is required.',
            'fixItTogether.cards.*.tag.required' => 'Each fix it together card must include a tag.',
            'fixItTogether.cards.*.title.required' => 'Each fix it together card must include a title.',
            'fixItTogether.cards.*.image.required' => 'Each fix it together card must include an image.',
            'fixItTogether.cards.*.link.required' => 'Each fix it together card must include a link.',

            // How It Works
            'howItWorks.required' => 'How it works section is required.',
            'howItWorks.title.required' => 'How it works title is required.',
            'howItWorks.subtitle.required' => 'How it works subtitle is required.',
            'howItWorks.steps.required' => 'At least one step is required.',
            'howItWorks.steps.*.step.required' => 'Each step number is required.',
            'howItWorks.steps.*.title.required' => 'Each step title is required.',
            'howItWorks.steps.*.description.required' => 'Each step description is required.',
            'howItWorks.steps.*.icon.required' => 'Each step must have an icon.',
            'howItWorks.steps.*.color.required' => 'Each step must have a color value.',
            'howItWorks.steps.*.iconBg.required' => 'Each step must have an icon background.',
            'howItWorks.steps.*.iconColor.required' => 'Each step must have an icon color.',
            'howItWorks.steps.*.stepColor.required' => 'Each step must have a step color.',
            'howItWorks.buttonText.required' => 'How it works button text is required.',
            'howItWorks.buttonLink.required' => 'How it works button link is required.',

            // FAQ
            'faq.required' => 'FAQ section is required.',
            'faq.title.required' => 'FAQ title is required.',
            'faq.subtitle.required' => 'FAQ subtitle is required.',
            'faq.faqList.required' => 'At least one FAQ item is required.',
            'faq.faqList.*.question.required' => 'Each FAQ must include a question.',
            'faq.faqList.*.answer.required' => 'Each FAQ must include an answer.',

            // CTA
            'cta.required' => 'CTA section is required.',
            'cta.title.required' => 'CTA title is required.',
            'cta.subtitle.required' => 'CTA subtitle is required.',
            'cta.paragraph.required' => 'CTA paragraph is required.',
            'cta.buttonText.required' => 'CTA button text is required.',
            'cta.buttonLink.required' => 'CTA button link is required.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
