<?php

namespace App\Http\Requests\API\Frontend;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class PreCheckoutRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => ['required','string','max:50'],
            'last_name' => ['required','string','max:50'],
            'pharmacy_name' => ['required','string','max:100'],
            'email' => ['required','email','max:70','unique:pharmacy_registrations'],
            'phone_number' => ['required','digits:10'],
            'preferred_sub_domain' => ['required','max:30'],
            'address_line_1' => ['required','max:255'],
            'address_line_2' => ['nullable','max:150'],
            'city' => ['required','max:70'],
            'state' => ['required','max:2','exists:states,code'],
            'zipcode' => ['required','max:10'],
            'country' => ['required','in:USA'],
            'card_last_4_digit' => ['nullable','numeric','digits:4'],
            'card_expiry_month' => ['nullable','numeric','digits:2','min:1','max:12'],
            'card_expiry_year' => ['nullable','numeric','digits:4','min:'.date('Y')],
            'card_brand_type' => ['nullable','max:30'],
        ];
    }

    public function messages(): array
    {
        return [
            'card_expiry_year.min' => 'The card has expired. Please use a valid card.',
            'card_expiry_month.min' => 'Please enter a valid card expiry month.',
            'card_expiry_month.max' => 'Please enter a valid card expiry month.',
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
