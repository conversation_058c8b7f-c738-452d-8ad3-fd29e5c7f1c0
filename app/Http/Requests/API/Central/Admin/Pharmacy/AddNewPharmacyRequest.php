<?php

namespace App\Http\Requests\API\Central\Admin\Pharmacy;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class AddNewPharmacyRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'pharmacy_name' => ['required', 'string', 'max:100'],
            'sub_domain' => ['required', 'string', 'max:100', 'regex:/^[A-Za-z0-9-]+$/',Rule::unique('tenants','id')->ignore($this->id)],
            'email' => ['required', 'email', 'max:150',Rule::unique('tenants')->ignore($this->id)],
            'phone_number' => ['required', 'digits:10',Rule::unique('tenants')->ignore($this->id)],
            'first_name' => ['required', 'string', 'max:100'],
            'last_name' => ['required', 'string', 'max:100'],
        ];
    }

    public function messages(): array
    {
        return [
        'sub_domain.regex' => trans('centraladmin/errors.tenant_name_regex_error'),
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422, 'message' => $validator->errors()->first(), 'errors' => $validator->errors()]));
    }
}
