<?php

namespace App\Http\Requests\API\Central\Admin\Plan;

use App\Rules\ValidateImageFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class AddEditPlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'plan_title' => ['required','max:255'],
            "summary" => ['nullable','max:2000'],
            'image' => ['nullable',new ValidateImageFile()],
            'monthly_price' => ['required','numeric','min:0.1'],
            'setup_fee' => ['nullable','numeric','min:0.1'],
            'features' => ['nullable','array'],
            'is_active' => ['required','in:0,1']
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
