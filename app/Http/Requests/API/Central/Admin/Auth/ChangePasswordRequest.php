<?php

namespace App\Http\Requests\API\Central\Admin\Auth;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class ChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'current_password' => ['required'],
            'new_password'   => ['required','min:8','max:20','different:current_password'],
            'password_confirmation' => ['required','same:new_password'],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
