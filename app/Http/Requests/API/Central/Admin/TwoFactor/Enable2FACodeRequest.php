<?php

namespace App\Http\Requests\API\Central\Admin\TwoFactor;

use App\Rules\ValidCurrentUserPassword;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Hash;

class Enable2FACodeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\Rule|array|string>
     */
    public function rules(): array
    {
        return [
            'code' => ['nullable','digits:6'],
            // 'password' => ['required_without:code',new ValidCurrentUserPassword($this)],
            'password' => [
                'required_if:code,!null', // Apply this rule to check for password when code is null
                function ($attribute, $value, $fail) {
                    if (is_null($this->input('code'))) { // Check if code is null
                        $user = auth()->user(); // Get current user
                        if (!Hash::check($value, $user->password)) { // Validate password
                            $fail('The password you entered is incorrect.'); // Custom error message
                        }
                    }
                },
            ],
        ];
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json(['status' => 422,'message'=>$validator->errors()->first(),'errors'=>$validator->errors()]));
    }
}
