<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;

class SanctumCookieAuth
{
    public function handle(Request $request, Closure $next)
    {
        if ($token = $request->cookie('accessToken')) {
            try {
                $token = Crypt::decryptString($token);
            } catch (\Throwable $th) {
                $token = "";
            }
            $request->headers->set('Authorization', 'Bearer ' . $token);
            try {
                $token = explode('|', $token);
                if(count($token)==2){
                    $request->headers->set('accessTokenID', $token[0]);
                }
            } catch (\Throwable $th) {

            }

        }
        return $next($request);
    }
}
