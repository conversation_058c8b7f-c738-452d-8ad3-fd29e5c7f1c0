<?php

namespace App\Http\Middleware\Tenant;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;
use Stancl\Tenancy\Facades\Tenancy;

class GuestMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $tenant = tenant();
        Tenancy::initialize($tenant);
        $header = $request->header('Authorization');
        $plainTextToken = substr($header, 7);
        // dd($plainTextToken);
        if($plainTextToken!=null && $plainTextToken!="null"){
            return response()->json(['message' => trans('admin/errors.you_are_not_unable_access_this_route')], 403);
        }
        $tokenId = null;
        if (str_contains($plainTextToken, '|')) {
            [$tokenId, $tokenStr] = explode('|', $plainTextToken, 2);
        }
        $accessToken = PersonalAccessToken::find($tokenId);
        if ($accessToken) {
            return response()->json(['message' => trans('admin/errors.you_are_not_unable_access_this_route')], 403);
        }
        return $next($request);
    }
}
