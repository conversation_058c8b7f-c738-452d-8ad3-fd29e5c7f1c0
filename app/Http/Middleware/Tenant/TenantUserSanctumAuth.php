<?php

namespace App\Http\Middleware\Tenant;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Laravel\Sanctum\PersonalAccessToken;
use Stancl\Tenancy\Facades\Tenancy;

class TenantUserSanctumAuth
{
    public function handle(Request $request, Closure $next)
    {
        $tenant = tenant();
        Tenancy::initialize($tenant);
        $header = $request->header('Authorization');
        if (!$header || !str_starts_with($header, 'Bearer ')) {
            return response()->json(['message' => trans('centraladmin/errors.unauthorized_access')], 401);
        }

        $plainTextToken = substr($header, 7); // remove "Bearer "

        // Sanctum format: "<id>|<token>"
        $tokenId = null;
        $tokenStr = $plainTextToken;

        if (str_contains($plainTextToken, '|')) {
            [$tokenId, $tokenStr] = explode('|', $plainTextToken, 2);
        }

        $accessToken = PersonalAccessToken::find($tokenId);

        if (!$accessToken || !hash_equals($accessToken->token, hash('sha256', $tokenStr))) {
            return response()->json(['message' => trans('centraladmin/errors.unauthorized_access')], 401);
        }

        // Authenticate the user
        $user = $accessToken->tokenable;


        $user->user_timezone = ($accessToken->user_timezone) ? $accessToken->user_timezone : config('app.timezone');
        // dd($user);
        Auth::setUser($user);
        return $next($request);
    }
}
