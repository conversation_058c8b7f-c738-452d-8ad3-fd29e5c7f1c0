<?php

namespace App\Http\Middleware;

use App\Traits\TokenTrait;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Laravel\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;

class Verify2FAAuthenticationMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // $header = $request->header('Authorization');
        $value = $request->cookie('accessToken');
        try {
            $plainTextToken = Crypt::decryptString($value);
        } catch (\Throwable $th) {
            return response()->json(['message' => trans('centraladmin/errors.unauthorized_access')], 401);
        }

        $tokenId = null;
        $tokenStr = $plainTextToken;

        if (str_contains($plainTextToken, '|')) {
            [$tokenId, $tokenStr] = explode('|', $plainTextToken, 2);
        }

        $accessToken = PersonalAccessToken::find($tokenId);

        if (!$accessToken || !hash_equals($accessToken->token, hash('sha256', $tokenStr))) {
            return response()->json(['message' => trans('centraladmin/errors.unauthorized_access')], 401);
        }
        $user = $accessToken->tokenable;
        if ($accessToken && $user->two_factor_enabled==1 && $accessToken->is_2fa_code_verified==0) {
            return response()->json(['status'=>403]);
        }
        return $next($request);

    }
}
