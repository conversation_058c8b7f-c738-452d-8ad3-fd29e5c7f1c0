<?php

namespace App\Http\Controllers\SuperAdmin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Models\Tenant\{User,AppSetting};
use Faker\Factory as Faker;
use Illuminate\Support\Facades\Artisan;
use Lara<PERSON>\Passport\ClientRepository;
use Lara<PERSON>\Passport\PassportServiceProvider;

class HomeController extends Controller
{
    public function index($tenant_name)
    {
        try {
            $centralDomain = config('tenancy.central_domains')[0];
            $tenant_sub_domain = $tenant_name . '.' . $centralDomain;
            $faker = Faker::create();
            $tenant_array = [
                'id' => $tenant_name,
                'school_name'  => strtolower($faker->company) . '_school',
                'email'        => $faker->unique()->safeEmail,
                'first_name'   => $faker->firstName,
                'last_name'    => $faker->lastName,
                'data'         => null,
                'phone_number' => $faker->numerify("##########")
            ];
            $tenant = Tenant::create($tenant_array);
            if ($tenant) {
                tenancy()->initialize($tenant);
                // $tenant->run(function () {
                //         app()->register(\Laravel\Passport\PassportServiceProvider::class);

                //         $clientRepository = app(ClientRepository::class);
                //         $clientRepository->create(null, 'Tenant Personal Access Client', url('/'), true, false);
                //     });


                $tenancy_user_registration['first_name'] = $tenant_array['first_name'];
                $tenancy_user_registration['last_name'] = $tenant_array['last_name'];
                $tenancy_user_registration['email'] = $tenant_array['email'];
                $tenancy_user_registration['phone_number'] = $tenant_array['phone_number'];
                $tenancy_user = User::create($tenancy_user_registration);
                if ($tenancy_user) {
                    $tenancy_user->assignRole('superadmin');
                    AppSetting::create(['app_name'=>ucwords($tenant_name)]);
                }

                $tenant->domains()->create(['domain' => $tenant_sub_domain]);
                // $email_data['tenant_name'] = ucwords($data['school_name']);
                // $email_data['first_name'] = ucwords($data['first_name']);

                // $encryptedId = EncryptDecryptTrait::encryptData($tenancy_user->id);

                // $action_url = tenant_route($tenant_sub_domain, 'admin.account.setup',['token'=>$encryptedId]);
                // $email_data['account_setup_url'] = $action_url;
                tenancy()->end();
                // $result = self::sendMail($tenant);
                return ['status' => 200, 'message' => 'Tenant has been successfully added.'];
            } else {
                return ['status' => 400, 'message' => 'Failed adding tenant.'];
            }
        } catch (\Throwable $th) {
            \Log::error('An error occurred: ' . $th->getMessage());
            return ['status' => 400, 'message' => 'Failed adding tenant.'];
        }
    }
}
