<?php

namespace App\Http\Controllers\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\{Tenant, UserProductLead};
use App\Models\Tenant\Product;
use App\Traits\DateTrait;
use Illuminate\Http\Request;
use App\Utils\PaginateCollection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserProductLeadController extends Controller
{
    use DateTrait;

    public function ListPharmacies(){
        $pharmacies = Tenant::select('id as pharmacy_id','pharmacy_name')
                        ->oldest('pharmacy_name')
                        ->get();
        $pharmacies->map(function ($pharmacies) {
            unset($pharmacies->data);
            return $pharmacies;
        });
        return response()->json([
            'success' => 200,
            'pharmacies' => $pharmacies
        ]);
    }

    public function ListProducts(Request $request){
        try {
            $perpage = (@$request->perPage) ? $request->perPage : 10;
            $products = UserProductLead::select('id','tenant_id','name','email','phone_number','product_details','created_at')
                ->with('pharmacy_details:id,pharmacy_name')
                ->whereHas('pharmacy_details');

            if($request->searchQuery!=''){
                $search = strtolower($request->searchQuery);
                $products = $products->where(function ($q) use ($search) {
                    $q->whereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.product_title'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.product_name'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.product_type'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.main_category_name'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(JSON_UNQUOTE(JSON_EXTRACT(product_details, '$.sub_category_name'))) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(phone_number) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(email) LIKE ?", ["%{$search}%"])
                        ->orWhereRaw("LOWER(name) LIKE ?", ["%{$search}%"]);
                });
            }

            if($request->pharmacy_id!=''){
                $products = $products->where('tenant_id',$request->pharmacy_id);
            }

            if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
                $products = $products->orderBy($request->sortByColumnName,$request->isSortDirDesc);
            } else {
                $products = $products->latest();
            }

            $products = $products->paginate($perpage);
            $timezone = $this->GetUserTimezone($request);
            $products->map(function ($products) use($timezone){
                $product_details = $products->product_details;
                $product = null;
                if($product_details){
                    $product['main_category_name'] = $product_details['main_category_name'];
                    $product['sub_category_name'] = $product_details['sub_category_name'] ?? null;
                    $product['product_name'] = $product_details['product_name'];
                    if($product_details['product_type'] == 'programs'){
                        $product['product_title'] = $product_details['product_title'];
                    }
                    $product['product_strength'] = $product_details['product_strength'];
                    $product['product_type'] = $product_details['product_type'];
                    $product['product_qty'] = $product_details['product_qty'];
                    $product['product_form'] = $product_details['product_form'];
                    $product['price'] = $product_details['price'];
                    // dd($product);
                    // dd($product);
                    // $product = (object)$product;
                }
                $products->product_detail = $product;
                $products->pharmacy_name = $products->pharmacy_details->pharmacy_name;

                $products->created_date = $this->GetDate($products->created_at,$timezone);
                $products->created_time = $this->GetTime($products->created_at,$timezone);
                unset($products->created_at,$products->product_details,$products->pharmacy_details,$products->tenant_id);
                return $products;
            });
            $info = PaginateCollection::paginate($products);
            return response()->json([
                'status' =>200,
                'products' => $info
            ]);

        } catch (\Throwable $th) {
            Log::error('An error occurred while listing pharmacy registrations data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function ViewProductDetails($id,Request $request){
        try {
            $product_detail = UserProductLead::select('id','tenant_id','name','email','phone_number','product_details','created_at')
                        ->with('pharmacy_details',function($q){
                            $q->select('id','pharmacy_name','email','phone_number','first_name','last_name');
                        })
                        ->whereHas('pharmacy_details')
                        ->where('id',$id)
                        ->first();
            if($product_detail){
                $product = null;
                $product_details = $product_detail->product_details;
                if($product_details){
                    $product['main_category_name'] = $product_details['main_category_name'];
                    $product['sub_category_name'] = $product_details['sub_category_name'] ?? null;
                    $product['product_name'] = $product_details['product_name'];
                    if($product_details['product_type'] == 'programs'){
                        $product['product_title'] = $product_details['product_title'];
                    }
                    $product['product_strength'] = $product_details['product_strength'];
                    $product['product_type'] = $product_details['product_type'];
                    $product['product_qty'] = $product_details['product_qty'];
                    $product['product_form'] = $product_details['product_form'];
                    $product['price'] = $product_details['price'];
                    $product['xpedicare_url'] = $product_details['xpedicare_url'];
                    $image = new UserProductLead();
                    $product['image'] = $image->GetImage($product_details['image'],$product_detail->tenant_id);
                }
                $product_detail->product_detail = $product;
                $timezone = $this->GetUserTimezone($request);
                $product_detail->created_date = $this->GetDate($product_detail->created_at,$timezone);
                $product_detail->created_time = $this->GetTime($product_detail->created_at,timezone: $timezone);
                unset($product_detail->product_details,$product_detail->tenant_id,$product_detail->pharmacy_details->id,$product_detail->pharmacy_details->data,$product_detail->created_at);
                return response()->json([
                    'status' =>200,
                    'productDetails' => $product_detail
                ]);
            } else {
                return response()->json([
                    'status' =>400,
                    'message' => trans('centraladmin/errors.record_not_found')
                ]);
            }

        } catch (\Throwable $th) {
            Log::error('An error occurred while view pharmacy registration details data',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }
}
