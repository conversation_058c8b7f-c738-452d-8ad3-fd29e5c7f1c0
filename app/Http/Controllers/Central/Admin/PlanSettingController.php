<?php

namespace App\Http\Controllers\Central\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Central\Admin\Plan\{AddEditPlanRequest,UpdatePlanImageRequest};
use App\Traits\FileUploadTrait;
use App\Models\PlanSetting;

class PlanSettingController extends Controller
{

    public function UpdatePlan(AddEditPlanRequest $request){
        try {
            $data = $request->only('plan_title','summary','setup_fee','monthly_price','features','is_active');
            if($request->image){
                $data['image'] = FileUploadTrait::PlanImageUploadFunction($request->image,'admin-assets/plans');
            }
            $data['features'] = (@$data['features'] && count($data['features'])>0) ? json_encode($data['features']) : null;
            $plan = PlanSetting::first();
            if($plan){
                $plan->update($data);
            } else {
                PlanSetting::create($data);
            }
            return response()->json([
                'status' => 200,
                'message' => trans('centraladmin/response.plan_details_updated_successfully')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while updating plan details : ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('centraladmin/errors.server_error')
            ]);
        }
    }

    public function GetPlanDetails(){
        $plan = PlanSetting::select('plan_title','summary','setup_fee','monthly_price','is_active','image','features')
                            ->first();
        return response()->json([
            'status' => 200,
            'planDetails' => $plan
        ]);
    }
}
