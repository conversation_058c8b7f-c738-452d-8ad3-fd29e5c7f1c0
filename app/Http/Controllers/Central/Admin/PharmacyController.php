<?php

namespace App\Http\Controllers\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Tenant;
use App\Traits\DateTrait;
use App\Traits\TenantTrait;
use Illuminate\Http\Request;
use App\Http\Requests\API\Central\Admin\Pharmacy\{AddNewPharmacyRequest, EditNewPharmacyRequest};
use App\Jobs\Central\SendInvitationMailJob;
use App\Models\Tenant\AppSetting;
use App\Models\Tenant\User;
use App\Utils\PaginateCollection;
use Carbon\Carbon;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PharmacyController extends Controller
{
    use DateTrait;
    public function AddPharmacy(AddNewPharmacyRequest $request){
        try {
            // DB::beginTransaction();
            $data = $request->only('pharmacy_name','email','phone_number','first_name','last_name');
            $pharmacy_name = strtolower(str_replace(' ','-',$request->sub_domain));
            // dd($pharmacy_name);
            $result = TenantTrait::CheckPharmacySubdomain($pharmacy_name);
            if ($result['status']!=200) {
                return response()->json([
                    'status' => 400,
                    'message' => $pharmacy_name . ' subdomain already taken. Please choose another one.'
                ]);
            }
            $centralDomain = config('tenancy.central_domains')[0];
            $tenant_sub_domain = $pharmacy_name . '.' . $centralDomain;
            $result = self::InvitationDetails();
            $tenant = Tenant::create([
                    'id' => $pharmacy_name,
                    'pharmacy_name' => $data['pharmacy_name'],
                    'email' => $data['email'],
                    'first_name' => $data['first_name'],
                    'last_name' => $data['last_name'],
                    'data' => null,
                    'phone_number' => $data['phone_number'],
                    'invitation_token' => $result['token'],
                    'invitation_expired_at' => $result['expired_at']
                ]);
            if ($tenant) {
                tenancy()->initialize($tenant);
                $tenancy_user_registration['first_name'] = $data['first_name'];
                $tenancy_user_registration['last_name'] = $data['last_name'];
                $tenancy_user_registration['email'] = $data['email'];
                $tenancy_user_registration['phone_number'] = $data['phone_number'];
                $tenancy_user_registration['email_verified_at'] = now();
                $tenancy_user = User::create($tenancy_user_registration);
                if ($tenancy_user) {
                    $tenancy_user->assignRole('superadmin');
                    AppSetting::create(['app_name'=>$data['pharmacy_name']]);
                }
                $tenant->domains()->create(['domain' => $tenant_sub_domain]);
                tenancy()->end();
                self::sendMail($tenant->id);
                return ['status' => 200, 'message' => 'Pharmacy has been successfully added.'];
            } else {
                return ['status' => 400, 'message' => "Something went wrong. Please try again."];
            }

        } catch (\Throwable $th) {
            Log::error('An error occurred while adding pharmacy',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function ListPharmacy(Request $request){
        try {
            $perpage = (@$request->perPage) ? $request->perPage : 10;
            $tenants = Tenant::select('id','pharmacy_name','first_name','last_name','email','phone_number','is_active','is_invitation_accepted','created_at','updated_at');

            if($request->is_active!=''){
                $tenants->where('is_active',$request->is_active);
            }
            if($request->searchQuery!=''){
                $search = strtolower($request->searchQuery);
                $tenants = $tenants->where(function($q) use($search){
                    $q->where('pharmacy_name', 'LIKE', "%" . $search . "%")
                        ->orWhere('email', 'LIKE', "%" . $search . "%")
                        ->orWhere('phone_number', 'LIKE', "%" . $search . "%")
                        ->orWhere(DB::raw("CONCAT(first_name,' ',last_name)"), 'LIKE', "%{$search}%");
                });
            }

            if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
                $tenants = $tenants->orderBy($request->sortByColumnName,$request->isSortDirDesc);
            } else {
                $tenants = $tenants->latest();
            }

            $tenants = $tenants->paginate($perpage);
            $timezone = $this->GetUserTimezone($request);
            $tenants->map(function ($tenants) use($timezone){
                $tenants->created_date = $this->GetDate($tenants->created_at,$timezone);
                $tenants->created_time = $this->GetTime($tenants->created_at,$timezone);
                $tenants->updated_date = $this->GetDate($tenants->updated_at,$timezone);
                $tenants->updated_time = $this->GetTime($tenants->updated_at,$timezone);
                if($tenants->parent_id){
                    $tenants->parent_category_name = @$tenants->parent_category->name;
                }
                unset($tenants->created_at,$tenants->updated_at,$tenants->data);
                return $tenants;
            });
            $info = PaginateCollection::paginate($tenants);
            return response()->json([
                'status' =>200,
                'pharmacies' => $info
            ]);

        } catch (\Throwable $th) {
            Log::error('An error occurred while listing pharmacy',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function EditPharmacy(EditNewPharmacyRequest $request){
        try {
            // DB::beginTransaction();
            $data = $request->only('pharmacy_name','email','phone_number','first_name','last_name');
            $pharmacy_name = strtolower(str_replace(' ','-',$request->sub_domain));
            $validate_tenant_name = Tenant::where('id', $pharmacy_name)->whereNot('id',$request->id)->count();
            $result = TenantTrait::CheckPharmacySubdomain($pharmacy_name,$request->id);
            if ($result['status']!=200) {
            // if ($validate_tenant_name > 0) {
                return response()->json([
                    'status' => 400,
                    'message' => $pharmacy_name . ' subdomain already exists. Please edit the subdomain.'
                ]);
            }
            $tenant = Tenant::findOrFail($request->id);
            if($tenant){
                // $old_tenant_id = $tenant->id;
                // $domain_id = $tenant->domains()->first()->id;
                $tenant->update([
                    // 'id' => $pharmacy_name,
                    'pharmacy_name' => $data['pharmacy_name'],
                    'email' => $data['email'],
                    'first_name' => $data['first_name'],
                    'last_name' => $data['last_name'],
                    'data' => null,
                    'phone_number' => $data['phone_number']
                ]);
                $centralDomain = config(key: 'tenancy.central_domains')[0];
                $tenant_sub_domain = $pharmacy_name . '.' . $centralDomain;
                if($tenant->is_invitation_accepted == 0){
                    tenancy()->initialize($tenant);

                    $user = User::role('superadmin')->first();
                    if($user){
                        $user->update(['email' => $data['email']]);
                    }
                    $setting = AppSetting::first();
                    if($setting){
                        $setting->update(['email' => $data['email']]);
                    }
                    tenancy()->end();
                    self::sendMail($tenant->id);
                }
                $tenant->domains()->update(['domain' => $tenant_sub_domain]);
                return ['status' => 200, 'message' => "Pharmacy has been successfully updated."];
            } else {
                return ['status' => 400, 'message' => "We're enable to find the pharmacy. Please try again."];
            }

        } catch (\Throwable $th) {
            Log::error('An error occurred while updating pharmacy',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function ViewPharmacy($id){
        try {
            $tenant = Tenant::select('id','pharmacy_name','email','phone_number','first_name','last_name','is_active')
                            ->with('domains',function($q){
                                $q->select('id','tenant_id','domain')->first();
                            })
                            ->where('id', $id)
                            ->first();
            if($tenant){
                $tenant->domain = $tenant->domains[0];
                unset($tenant->domains,$tenant->data);
                return ['status' => 200, 'pharmacyDetails' => $tenant];
            } else {
                return ['status' => 404, 'message' => trans('centraladmin/errors.record_not_found')];
            }
        } catch (\Throwable $th) {
            Log::error('An error occurred while fetching pharmacy',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function UpdatePharmacyStatus($id){
        try {
            $tenant = Tenant::select('id','is_active')
                            ->where('id', $id)
                            ->first();
            if($tenant){
                $status = ($tenant->is_active==0) ? 1 : 0;
                Tenant::where('id', $id)->update(['is_active' => $status]);
                $msg = ($status==0) ? "The pharmacy status has been marked as inactive." : "Pharmacy has been activated successfully.";
                return ['status' => 200, 'message' => $msg];
            } else {
                return ['status' => 404, 'message' => trans('centraladmin/errors.record_not_found')];
            }
        } catch (\Throwable $th) {
            Log::error('An error occurred while fetching pharmacy',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    public function ResendInvitation($id){
        try {
            $tenant = Tenant::select('id','is_invitation_accepted')
                            ->where('id', $id)
                            ->first();
            if($tenant){
                if($tenant->is_invitation_accepted ==1){
                    return response()->json(['status'=>400,'message'=>"The invitation has already been accepted."]);
                }
                self::sendMail($tenant->id);
                return ['status' => 200, 'message' => 'Invitation has been successfully resent.'];
            } else {
                return ['status' => 404, 'message' => trans('centraladmin/errors.record_not_found')];
            }
        } catch (\Throwable $th) {
            Log::error('An error occurred while resending invitation mail',[
                'Errors'=>$th->getMessage()
            ]);
            return response()->json(['status'=>500,'message'=> trans('centraladmin/errors.server_error')]);
        }
    }

    protected static function sendMail($tenant_id){
        try {
            $tenant = Tenant::where('id', $tenant_id)->first();
            if($tenant){
                $date = Carbon::parse($tenant->invitation_expired_at);
                if($date->isPast()){
                    $result = self::InvitationDetails();
                    $tenant->update([
                        'invitation_token'=>$result['token'],
                        'invitation_expired_at'=>$result['expired_at']
                    ]);
                }
                tenancy()->initialize($tenant);

                $tenant_name = $tenant->id;
                $domain_url = TenantTrait::TenantDomainUrl($tenant->domains[0]->domain);

                $email_data['first_name'] = ucwords($tenant->first_name);
                $email_data['pharmacy_name'] = ucwords($tenant->pharmacy_name);
                $email_data['email'] = $tenant->email;

                $token = Crypt::encryptString($tenant->invitation_token);
                $action_url = $domain_url."/admin/onboard/create-password/".$token;
                $email_data['button_action_url'] = $action_url;
                tenancy()->end();
                SendInvitationMailJob::dispatch($email_data);
                return $action_url;
            } else {
                return false;
            }
        } catch (\Throwable $th) {
            Log::error('An error occurred while sending invitation mail',[
                'Error Message'=>$th->getMessage(),
                'Errors' => $th
            ]);
            return false;
        }
    }

    protected static function InvitationDetails(){
        $data['token'] = Str::random(64);
        $data['encrypted_token'] = Crypt::encryptString($data['token']);
        $data['expired_at'] = Carbon::now()->addDays(7);
        return $data;
    }
}
