<?php

namespace App\Http\Controllers\Central\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Central\Admin\TwoFactor\{Enable2FACodeRequest,Disable2FACodeRequest};
use App\Traits\TokenTrait;
use PragmaRX\Google2FA\Google2FA;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TwoFactorAuthController extends Controller
{
    public function index(Request $request){
        try {
            $user = $request->user();
            return response()->json([
                'status' => 200,
                'is_2fa_enabled' => $user->two_factor_enabled ?? false,
            ]);
        } catch (\Throwable $th) {
            Log::error('Error while fetching 2FA status.', ['Errors' => $th]);
            return response()->json([
                'status' => 500,
                'message' => trans('centraladmin/errors.server_error'),
            ]);
        }
    }

    public function enable(Enable2FACodeRequest $request, Google2FA $google2fa){
        try {
            $user = $request->user();
            unset($user->user_timezone);
            $secret = $google2fa->generateSecretKey();
            $user->two_factor_secret = encrypt($secret);
            $user->save();
            $qrCodeUrl = $google2fa->getQRCodeUrl(
                config('app.name'),
                $user->email,
                $secret
            );
            return response()->json([
                'status' => 200,
                'qrCodeUrl' => $qrCodeUrl,
                'secret' => $secret,
            ]);
        } catch (\Throwable $th) {
            Log::error('Error while fetching 2FA details.', ['Errors' => $th]);
            return response()->json([
                'status' => 500,
                'message' => trans('centraladmin/errors.server_error'),
            ]);
        }
    }

    public function verify(Enable2FACodeRequest $request, Google2FA $google2fa){
        try {
            $user = $request->user();
            if(empty($user->two_factor_secret)){
                return response()->json([
                    'status' => 422,
                    'message' => trans('centraladmin/errors.invalid_2fa_code')
                ]);
            }
            $secret = decrypt($user->two_factor_secret);
            $valid = $google2fa->verifyKey($secret, $request->input('code'));
            if ($valid) {
                unset($user->user_timezone);
                $user->two_factor_enabled = true;
                $user->save();
                // delete others token for same user
                $headerToken = $request->header('Authorization');
                $current_user_token_id = TokenTrait::GetTokenId($headerToken);
                // dd($current_user_token_id);
                // Update 2FA verification for the current user's token
                DB::table('personal_access_tokens')->where('tokenable_id',$user->id)->where('id',$current_user_token_id)->update(['is_2fa_code_verified'=>1]);
                // Log out other devices for the current user.
                DB::table('personal_access_tokens')->where('tokenable_id',$user->id)->where('is_2fa_code_verified',0)->whereNot('id',$current_user_token_id)->delete();

                return response()->json([
                    'status' => 200,
                    'message' => trans('centraladmin/response.2fa_enabled_success')
                ]);
            }
            return response()->json([
                'status' => 422,
                'message' => trans('centraladmin/errors.invalid_2fa_code')
            ]);
        } catch (\Throwable $th) {
            Log::error('Error while enabling 2FA.', ['Errors' => $th]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error'),
            ]);
        }
    }

    public function disable(Disable2FACodeRequest $request){
        try {
            $user = $request->user();
            unset($user->user_timezone);
            $user->two_factor_enabled = false;
            $user->two_factor_secret = null;
            $user->save();
            return response()->json([
                'status' => 200,
                'message' => trans('centraladmin/response.2fa_disabled_success')
            ]);
        } catch (\Throwable $th) {
            Log::error('Error while disabling 2FA.', ['Errors' => $th]);
            return response()->json([
                'status' => 500,
                'message' => trans('centraladmin/errors.server_error'),
            ]);
        }
    }
}
