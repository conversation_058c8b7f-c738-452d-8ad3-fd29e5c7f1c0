<?php

namespace App\Http\Controllers;

use App\Services\PaypalService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PaypalPaymentMethodController extends Controller
{
    public function VaultToken(Request $request){
        try {
            if($request->full_name){
                $post_data = array(
                    "payment_source" => [
                        "card" => (object) array(
                            "name" => $request->full_name
                        )
                    ]
                );
            } else {
                $post_data = array(
                    "payment_source" => [
                        "card" => (object) []
                    ]
                );
            }
            $result = PaypalService::createVaultToken($post_data);
            if($result && @$result['id']) {
                return response()->json(['status' => 200,'vaultId'=>$result['id']]);
            } else {
                Log::error('Error while generate paypal vault token',['Errors'=>@$result]);
                return response()->json(['status' => 400,'message'=>trans('errors.server_error')]);
            }
        } catch (\Throwable $th) {
            Log::error('Error while generating paypal vault token.',['Errors'=>$th]);
            return  response()->json(['status' => 500,'message'=>trans('errors.server_error')]);
        }
    }

    public function VaultPaymentToken($vault_id){
        try {
            $post_data = [
                'payment_source' => [
                    'token' => (object)[
                        'id' => $vault_id,
                        'type' => 'SETUP_TOKEN',
                    ],
                ],
            ];
            $result = PaypalService::createVaultPaymentToken($post_data);
            if($result && @$result['id']) {
                $card_details['paypal_token'] = $result['id'];
                $card_details['card_last_4_digit'] = $result['payment_source']['card']['last_digits'] ?? null;
                if(@$result['payment_source']['card']['expiry']){
                    $card_expiry = explode('-',$result['payment_source']['card']['expiry']);
                }
                $card_details['card_expiry_month'] = $card_expiry[1] ?? null;
                $card_details['card_expiry_year'] = $card_expiry[0] ?? null;
                $card_details['card_brand_type'] = $result['payment_source']['card']['brand'] ?? null;

                return response()->json(['status' => 200,"cardDetail"=>$card_details]);
            } else {
                // $failed_reason = (@$result['error']['message']) ? @$result['error']['message'] : trans('api/errors.unable_to_verify_card');
                return response()->json(['status' => 400,'message'=>trans('api/errors.unable_to_verify_card')]);
            }
        } catch (\Throwable $th) {
            Log::error('Error while creating paypal vault token.',['Errors'=>$th]);
            return  response()->json(['status' => 400,'message'=>trans('api/errors.server_error')]);
        }
    }
}
