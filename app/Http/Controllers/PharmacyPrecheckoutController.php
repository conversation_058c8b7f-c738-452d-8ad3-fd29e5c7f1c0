<?php

namespace App\Http\Controllers;

use App\Http\Requests\API\Frontend\PreCheckoutRequest;
use App\Models\{PaymentLog, PharmacyRegistration, PharmacyRegistrationFailure, State,PlanSetting, Tenant};
use App\Services\PaypalService;
use App\Traits\EncryptDecryptTrait;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PharmacyPrecheckoutController extends Controller
{
    public function PharmacyPreCheckoutDetails(){
        $plan = PlanSetting::select('plan_title','summary','setup_fee','monthly_price','image','features')
                            ->where('is_active',1)
                            ->first();
        if($plan){
            $data['planDetails'] = $plan;
            $data['states'] = State::select('name','code')->where('is_active',1)->orderBy('name','asc')->get();
            return response()->json([
                'status' => 200,
                'pharmacyPreCheckoutDetails' => $data
            ]);
        } else {
            return response()->json([
                'status' => 200,
                'pharmacyPreCheckoutDetails' => null
            ]);
        }

    }

    public function ValidatePharmacySubdomain(Request $request){
        try {
            return self::CheckPharmacySubdomain($request->sub_domain);
        } catch (\Throwable $th) {
            Log::error('An error occurred while validating pharmacy subdomain : ',  [
                    'Request Payload' => $request->all(),
                    'message' => $th->getMessage(),
                    'Error' => $th->getTraceAsString()
                ]
            );
            return response()->json([
                'status' => 400,
                "message" => "Something went wrong. Please try again"
            ]);
        }
    }

    protected static function CheckPharmacySubdomain($sub_domain){
        try {
            $pharmacy_name = strtolower(str_replace(' ','-',$sub_domain));
            $tenant = Tenant::where('id', $pharmacy_name)->count();
            if ($tenant > 0) {
                return [
                    'status' => 400,
                    'message' => $sub_domain." is already taken"
                ];
            }
            $pharmacy_registration = PharmacyRegistration::where('preferred_sub_domain', $pharmacy_name)->count();
            if ($pharmacy_registration > 0) {
                return [
                    'status' => 400,
                    'message' => $sub_domain." is already taken"
                ];
            }
            $centralDomain = config('tenancy.central_domains')[0];
            $tenant_sub_domain = $pharmacy_name . '.' . $centralDomain;
            $domain = DB::table('domains')
                        ->where('domain', $tenant_sub_domain)
                        ->count();
            if ($domain > 0) {
                return [
                    'status' => 400,
                    'message' => $sub_domain." is already taken"
                ];
            }
            return [
                'status' => 200,
                "message" => "Available"
            ];
        } catch (\Throwable $th) {
            return [
                'status' => 400,
                "message" => "Something went wrong. Please try again"
            ];
        }
    }


    public function NewPharmacyRequest(PreCheckoutRequest $request){
        try {
            if($request->id){
                $failed_transaction = PharmacyRegistrationFailure::where('id', $request->id)->first();
                if(!$failed_transaction){
                    return response()->json([
                        'status' => 400,
                        'message' => trans('api/errors.record_not_found')
                    ]);
                }
            }
            $plan = PlanSetting::select('plan_title','summary','setup_fee','monthly_price','image','image as plan_image')
                        ->where('is_active',1)
                        ->first();
            if(!$plan){
                return response()->json([
                    'status' => 400,
                    'message' => "Plan not found"
                ]);
            }
            // validate preferred domain
            $result = self::CheckPharmacySubdomain($request->preferred_sub_domain);
            if($result['status'] == 400){
                return response()->json($result);
            }
            // duplicate email
            $email = $request->email;
            $tenant = Tenant::where('email', $email)->count();
            if ($tenant > 0) {
                return response()->json([
                    'status' => 400,
                    'message' => "Email already exists"
                ]);
            }
            // dd($plan);
            $data = $request->only('first_name','last_name','email','phone_number','preferred_sub_domain','pharmacy_name');
            $data['address_details'] = json_encode($request->only('address_line_1','address_line_2','city','state','zipcode','country'),JSON_UNESCAPED_SLASHES);
            DB::beginTransaction();
            $is_failed = 0;
            $plan_amount = (float)$plan->monthly_price + ($plan->setup_fee ?? 0);
            if($request->payment_token==null){
               $is_failed = 1;
            } else {
                $pharmacy_registration = PharmacyRegistration::create($data);
                if($pharmacy_registration){
                    $payment_log = PaypalService::authorizePayment($request->payment_token,$plan_amount,$plan);
                    if($payment_log['status'] == 200){
                        $authorize_details = @$payment_log['paymentInfo']['purchase_units'][0]['payments']['authorizations'];
                        $authorize_payment_id = @$authorize_details[0]['id'];
                        $authorize_expire_at = @$authorize_details[0]['expiration_time'];

                        $payment_log['pharmacy_registration_id'] = $pharmacy_registration->id;
                        $payment_log['authorize_transaction_id'] = $authorize_payment_id;
                        if(@$authorize_expire_at){
                            $payment_log['authorize_transaction_expired_at'] = Carbon::parse($authorize_expire_at)->format('Y-m-d H:i:s');
                        }
                        $payment_log['plan_amount'] = $authorize_details[0]['amount']['value'];
                        $payment_log['payment_status'] = 1;
                        $payment_log['last_card_four_digits'] = $payment_log['paymentInfo']['payment_source']['card']['last_digits'];
                        if(@$payment_log['paymentInfo']['payment_source']['card']['expiry']){
                            $expiry = explode('-',$payment_log['paymentInfo']['payment_source']['card']['expiry']);
                            $payment_log['card_expiry_month'] = $expiry[1];
                            $payment_log['card_expiry_year'] = $expiry[0];
                        }
                        $payment_log['card_brand_type'] = $payment_log['paymentInfo']['payment_source']['card']['brand'];
                        PaymentLog::create($payment_log);
                        $pharmacy_registration->update([
                            'authorize_transaction_id' => $authorize_payment_id,
                            'transaction_type' => "authorize",
                            'payment_vault_token' => EncryptDecryptTrait::EncryptString($request->payment_token),
                            'last_card_four_digits' => @$payment_log['paymentInfo']['payment_source']['card']['last_digits'],
                            'card_expiry_month' => @$expiry[1] ?? null,
                            'card_expiry_year' => @$expiry[0] ?? null,
                            'card_brand_type' => @$payment_log['paymentInfo']['payment_source']['card']['brand']
                        ]);
                        if($request->id){
                            PaymentLog::where('pharmacy_registration_failure_id',$request->id)
                                ->update([
                                    'pharmacy_registration_id' => $pharmacy_registration->id,
                                    'pharmacy_registration_failure_id' => null
                                ]);
                            $failed_transaction->delete();
                        }
                        DB::commit();
                        return response()->json([
                            'status' => 200,
                            "message" => trans("api/response.pharmacy_request_successfully")
                        ]);
                    } else {
                        $is_failed = 1;
                    }
                } else {
                    DB::rollBack();
                    return response()->json([
                        'status' => 400,
                        "message" => "Something went wrong. Please try again"
                    ]);
                }
            }
            if($is_failed == 1){
                if(@$pharmacy_registration){
                    $pharmacy_registration->delete();
                }
                $payment_card_details['card_last_4_digit'] = $request->card_last_4_digit ?? null;
                $payment_card_details['card_expiry_month'] = $request->card_expiry_month ?? null;
                $payment_card_details['card_expiry_year'] = $request->card_expiry_year ?? null;
                $payment_card_details['card_brand_type'] = $request->card_brand_type ?? null;
                $data['payment_card_details'] = json_encode($payment_card_details,JSON_UNESCAPED_SLASHES);
                if($request->id){
                    $failed_transaction->update($data);
                } else {
                    $failed_transaction = PharmacyRegistrationFailure::create($data);
                }

                $error_message = "We are unable to verify your card details.";
                if(@$payment_log){
                    $error_message = @$payment_log['paymentInfo']['error']['message'] ?? "We are unable to verify your card details.";
                }
                $paymentLog['pharmacy_registration_failure_id'] = $failed_transaction->id;
                $paymentLog['plan_amount'] = $plan_amount;
                $paymentLog['payment_status'] = 0;
                $paymentLog['transaction_type'] = "authorize";
                $paymentLog['last_card_four_digits'] = $payment_card_details['card_last_4_digit'];
                $paymentLog['card_expiry_month'] = $payment_card_details['card_expiry_month'];
                $paymentLog['card_expiry_year'] = $payment_card_details['card_expiry_year'];
                $paymentLog['card_brand_type'] = $payment_card_details['card_brand_type'];
                $paymentLog['failed_reason'] = $error_message;
                PaymentLog::create($paymentLog);
                Log::error('Payment Failed',  [
                    'Name' => $request->first_name.' '.$request->last_name,
                    'Pharmacy Name' => $request->pharmacy_name,
                    'Preferred Subdomain' => $request->preferred_sub_domain,
                    'Email' => $request->email,
                    'Phone Number' => $request->phone_number,
                    'Failed Reason' => $error_message
                ]);
                DB::commit();
                return response()->json([
                    'status' => 400,
                    "message" => $error_message,
                    "id" => $failed_transaction->id
                ]);
            }
        } catch (\Throwable $th) {
            DB::rollBack();
            Log::error('An error occurred while creating pharmacy registration : ',  [
                    'Request Payload' => $request->all(),
                    'message' => $th->getMessage(),
                    'Error' => $th->getTraceAsString()
                ]
            );
            return response()->json([
                'status' => 400,
                "message" => "Something went wrong. Please try again"
            ]);
        }

    }
}
