<?php

namespace App\Http\Controllers\Tenant\API\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Tenant\{SubCategory,Product};
use App\Utils\PaginateCollection;
use App\Http\Requests\API\Tenant\Admin\SubCategory\{AddSubCategoryRequest,EditSubCategoryRequest};
use Illuminate\Support\Str;
use App\Traits\DateTrait;

class SubCategoryController extends Controller
{
    use DateTrait;

    public function AddSubCategory(AddSubCategoryRequest $request){
        try {
            $data = $request->only('name','main_category_id');
            $data['slug'] = self::generateUniqueSlug($data['name']);
            $data['is_active'] = 1;
            SubCategory::create($data);
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.add_sub_category_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while adding sub category ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public static function generateUniqueSlug($name,$id=null,$counter=0){
        $slug = Str::slug($name);
        if($counter>0){
            $slug = $slug.'-'.$counter;
        }
        $data = SubCategory::where('slug',$slug);
        if($id){
            $data->whereNot('id',$id);
        }
        $data = $data->count();
        if($data>0){
            $counter = $counter + 1;
            return self::generateUniqueSlug($name,$id,$counter);
        } else {
            return $slug;
        }
    }

    public function ListSubCategory(Request $request){
        $perpage = (@$request->perPage) ? $request->perPage : 10;
        $categories = SubCategory::select('id','main_category_id','name','slug','is_active','created_at','updated_at')
                            ->whereHas('main_category')
                            ->with('main_category:id,name,image');

        if($request->is_active!=''){
            $categories->where('is_active',$request->is_active);
        }

        if($request->main_category_id!=''){
            $categories->where('main_category_id',$request->main_category_id);
        }

        if($request->searchQuery!=''){
            $search = strtolower($request->searchQuery);
            $categories = $categories->where(function($q) use($search){
                $q->where('name', 'LIKE', "%" . $search . "%")
                    ->orWhere('slug', 'LIKE', "%" . $search . "%");
            });
        }

        if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
            $categories = $categories->orderBy($request->sortByColumnName,$request->isSortDirDesc);
        } else {
            $categories = $categories->latest();
        }

        $categories = $categories->paginate($perpage);
        $timezone = $this->GetUserTimezone($request);
        $categories->map(function ($categories) use($request,$timezone){
            $categories->created_date = $this->GetDate($categories->created_at,$timezone);
            $categories->created_time = $this->GetTime($categories->created_at,$timezone);
            $categories->updated_date = $this->GetDate($categories->updated_at,$timezone);
            $categories->updated_time = $this->GetTime($categories->updated_at,$timezone);
            if($categories->parent_id){
                $categories->parent_category_name = @$categories->parent_category->name;
            }
            unset($categories->created_at,$categories->parent_id,$categories->updated_at,$categories->parent_category);
            return $categories;
        });
        $info = PaginateCollection::paginate($categories);
        return response()->json([
            'status' =>200,
            'subCategories' => $info
        ]);
    }

    public function ViewSubCategoryById($id){
        $category = SubCategory::select('id','main_category_id','name','slug','is_active')
                            ->whereHas('main_category')
                            ->with('main_category:id,name,image')
                            ->where('id',$id)
                            ->first();
        if($category){
            return response()->json([
                'status' => 200,
                'subCategory' => $category
            ]);
        } else {
            return response()->json([
                'status' =>404,
                'message' => trans('admin/errors.record_not_found')
            ]);
        }
    }

    public function EditSubCategory(EditSubCategoryRequest $request){
        try {
            $category = SubCategory::where('id',$request->id)->first();
            if($category){
                $data = $request->only('name','id','main_category_id');
                $data['slug'] = self::generateUniqueSlug($data['name'],$data['id']);
                $category->update($data);
                return response()->json([
                    'status' => 200,
                    'message' => trans('admin/response.update_main_category_success')
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating category : ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function DeleteSubCategory($id){
        try {
            $data = SubCategory::where('id',$id)->first();
            if($data){
                $check_product = Product::where('sub_category_id',$id)->count();
                if($check_product>0){
                    return response()->json([
                        'status' => 400,
                        'message' => trans('admin/errors.category_has_product')
                    ]);
                }
                $data->delete();
                return response()->json([
                    'status' => 200,
                    'message' => trans('admin/response.delete_sub_category_success')
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while deleting sub category : ', ['Errors' => $th]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateSubCategoryStatus($id){
        try {
            $category = SubCategory::select('id','is_active')
                    ->where('id',$id)
                    ->first();
            if($category){
                $category->update(['is_active'=>!$category->is_active]);
                $msg = ($category->is_active==0) ? trans('admin/response.sub_category_is_active_status_inactive') : trans('admin/response.sub_category_is_active_status_active');
                return response()->json([
                    'status' => 200,
                    'message' => $msg
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating category status (active/inactive) : ', ['Errors' => $th->getMessage()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }
}
