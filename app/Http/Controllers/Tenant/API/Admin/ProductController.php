<?php

namespace App\Http\Controllers\Tenant\API\Admin;

use App\Http\Controllers\Controller;
use App\Traits\FileUploadTrait;
use Illuminate\Http\Request;
use App\Utils\PaginateCollection;
use App\Http\Requests\API\Tenant\Admin\Product\{AddProductRequest,EditProductRequest, UpdateProductDescriptionFaqRequest, UpdateProductImageRequest};
use App\Models\Tenant\{Product,MainCategory, ProductItem,ProductDescription,ProductFaq};
use Illuminate\Support\Str;
use App\Traits\DateTrait;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ProductController extends Controller
{
    use DateTrait;

    public function AllCategories(){
        $allCategories = [];
        $categories = MainCategory::select('id','name','gender')
                        ->with('sub_categories',function($q){
                            $q->select('id','name','main_category_id');
                        })
                        ->where('is_active',1)
                        ->get();
        if((count($categories)>0)){
            $allCategories = $categories;
        }
        return response()->json([
            'status' => 200,
            'allCategories' => $allCategories
        ]);
    }

    public function AddProduct(AddProductRequest $request){
        try {
            $data['main_category_id'] = $request->main_category_id;
            $data['sub_category_id'] = $request->sub_category_id;
            $data['product_type'] = $request->product_type;
            if($request->product_type=="single"){
                $prod = ProductItem::where('product_name','=',$request->product_name)
                                    ->whereHas('product',function($q) use($request){
                                        $q->where('main_category_id','=',$request->main_category_id);
                                    });
                if($request->sub_category_id){
                    $prod->whereHas('product',function($q) use($request){
                                $q->where('sub_category_id','=',$request->sub_category_id);
                            });
                } else{
                    $prod->whereHas('product',function($q) use($request){
                            $q->whereNull('sub_category_id');
                        });
                }
                $prod = $prod->count();
                if($prod>0){
                    return response()->json([
                        'status' => 400,
                        'message'=> trans('admin/errors.product_already_exist')
                    ]);
                }
                $data['slug'] = self::generateUniqueSlug($request->product_name);
                $folder_name = $request->product_name;
            } else {
                $prod = Product::where('product_title','=',$request->product_title)
                                    ->where('main_category_id','=',$request->main_category_id);
                if($request->sub_category_id){
                    $prod->where('sub_category_id','=',$request->sub_category_id);
                } else{
                    $prod->whereNull('sub_category_id');
                }
                $prod = $prod->count();
                if($prod>0){
                    return response()->json([
                        'status' => 400,
                        'message'=> trans('admin/errors.product_title_already_exist')
                    ]);
                }
                $data['product_title'] = $request->product_title;
                $data['slug'] = self::generateUniqueSlug($request->product_title);
                $folder_name = $request->product_title;
            }
            $data['is_active'] = 1;
            DB::beginTransaction();
            $product = Product::create($data);
            if($product){
                if($request->product_type=="single"){
                    $product_item = $request->only('product_name','product_form','product_strength','product_qty','price','xpedicare_url');
                    $product_item['product_id'] = $product->id;
                    $productItem = ProductItem::create($product_item);
                    if(!$productItem){
                        DB::rollBack();
                        return response()->json([
                            'status' => 400,
                            'message'=> trans('admin/errors.product_item_creation_error')
                        ]);
                    }
                    $path = self::FolderPathInfo($folder_name);
                    $image = FileUploadTrait::UploadFile($request->image,$path,'admin');
                    $product->update(['image'=>$image]);
                } else if($request->product_type=="programs"){
                    foreach ($request->product_items as $item) {
                        $product_item = Arr::only($item, [
                            'product_name',
                            'product_form',
                            'product_strength',
                            'product_qty',
                            'price',
                            'xpedicare_url',
                        ]);
                        $product_item['product_id'] = $product->id;
                        $productItem = ProductItem::create($product_item);
                        if(!$productItem){
                            DB::rollBack();
                            return response()->json([
                                'status' => 400,
                                'message'=> trans('admin/errors.product_item_creation_error')
                            ]);
                        }
                    }
                    $path = self::FolderPathInfo($folder_name);
                    $image = FileUploadTrait::UploadFile($request->image,$path,'admin');
                    $product->update(['image'=>$image]);
                }
            } else {
                DB::rollBack();
                return response()->json([
                    'status' => 400,
                    'message'=> trans('admin/errors.product_creation_failed')
                ]);
            }
            DB::commit();
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.add_product_success'),
                "id" => $product->id
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while adding product ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function ListProduct(Request $request){
        $perpage = (@$request->perPage) ? $request->perPage : 10;
        $products = Product::select(
                    'products.id',
                    'products.product_title',
                    'products.slug',
                    'products.product_type',
                    'products.image',
                    'products.is_active',
                    'products.created_at',
                    'products.updated_at',
                    'main_categories.name as main_category_name',
                    'sub_categories.name as sub_category_name'
                )
                ->with('product_item',function($q){
                    $q->select('product_id','product_name','product_form','product_strength','product_qty','price')
                        ->orderBy('price','asc');
                })
                ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id');

        if($request->is_active!=''){
            $products->where('products.is_active',$request->is_active);
        }

        if($request->main_category_id!=''){
            $products->where('products.main_category_id',$request->main_category_id);
        }

        if($request->searchQuery!=''){
            $search = strtolower($request->searchQuery);
            $products = $products->where(function($q) use($search){
                $q->where('products.product_title', 'LIKE', "%" . $search . "%")
                    ->orWhere('products.slug', 'LIKE', "%" . $search . "%")
                    ->orwhereHas('product_item',function($q) use($search){
                        $q->where('product_name', 'LIKE', "%" . $search . "%");
                    });
            });
        }

        if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
            $products = $products->orderBy($request->sortByColumnName,$request->isSortDirDesc);
        } else {
            $products = $products->latest();
        }

        $products = $products->paginate($perpage);
        $timezone = $this->GetUserTimezone($request);
        $products->map(function ($products) use($request,$timezone) {
            if($products->product_type=='single'){
                $title = $products->product_item->product_name;
            } else {
                $title = $products->product_title;
            }
            $products->product_name_title = $title;
            $products->product_form = $products->product_item->product_form;
            $products->product_strength = $products->product_item->product_strength;
            $products->product_qty = $products->product_item->product_qty;
            $products->product_price = $products->product_item->price;
            $products->created_date = $this->GetDate($products->created_at,$timezone);
            $products->created_time = $this->GetTime($products->created_at,$timezone);
            $products->updated_date = $this->GetDate($products->updated_at,$timezone);
            $products->updated_time = $this->GetTime($products->updated_at,$timezone);
            unset($products->created_at,$products->updated_at,$products->product_item,$products->product_title);
            return $products;
        });
        $info = PaginateCollection::paginate($products);
        return response()->json([
            'status' =>200,
            'products' => $info
        ]);
    }

    public function ViewProductById($productId){
        $product = Product::select(
                    'products.id',
                    'products.main_category_id',
                    'products.sub_category_id',
                    'products.product_title',
                    'products.product_type',
                    'products.slug',
                    'products.image',
                    'products.is_active',
                    'main_categories.name as main_category_name',
                    'sub_categories.name as sub_category_name'
                )
                ->with('product_items:id,product_id,product_name,product_form,product_strength,product_qty,price,xpedicare_url')
                ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                ->where('products.id',$productId)
                ->first();
        if($product){
            $product->product_descriptions = ProductDescription::select('title','description')->where('product_id',$product->id)->get();
            $product->product_faqs = ProductFaq::select('question','answer')->where('product_id',$product->id)->get();
            return response()->json([
                'status' => 200,
                'productDetails' => $product
            ]);
        } else {
            return response()->json([
                'status' =>404,
                'message' => trans('admin/errors.record_not_found')
            ]);
        }
    }

    public function EditProduct(EditProductRequest $request){
        try {
            $product = Product::where('id',$request->id)->first();
            if(!$product){
                return response()->json([
                    'status'=> 404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
            $previous_product_type = $product->product_type;
            $data['main_category_id'] = $request->main_category_id;
            $data['sub_category_id'] = $request->sub_category_id;
            $data['product_type'] = $request->product_type;
            if($request->product_type=="single"){
                $prod = ProductItem::where('product_name','=',$request->product_name)
                                    ->whereHas('product',function($q) use($request){
                                        $q->where('main_category_id','=',$request->main_category_id);
                                    });
                if($request->sub_category_id){
                    $prod->whereHas('product',function($q) use($request){
                                $q->where('sub_category_id','=',$request->sub_category_id);
                            });
                } else{
                    $prod->whereHas('product',function($q) use($request){
                            $q->whereNull('sub_category_id');
                        });
                }
                $prod = $prod->whereNot('product_id',$request->id)->count();
                if($prod>0){
                    return response()->json([
                        'status' => 400,
                        'message'=> trans('admin/errors.product_already_exist')
                    ]);
                }
                $data['product_title'] = null;
                $data['slug'] = self::generateUniqueSlug($request->product_name);
            } else {
                $prod = Product::where('product_title','=',$request->product_title)
                                    ->where('main_category_id','=',$request->main_category_id);
                if($request->sub_category_id){
                    $prod->where('sub_category_id','=',$request->sub_category_id);
                } else{
                    $prod->whereNull('sub_category_id');
                }
                $prod = $prod->whereNot('id',$product->id)->count();
                if($prod>0){
                    return response()->json([
                        'status' => 400,
                        'message'=> trans('admin/errors.product_title_already_exist')
                    ]);
                }
                $data['product_title'] = $request->product_title;
                $data['slug'] = self::generateUniqueSlug($request->product_title);
            }
            $updated_product_type = $request->product_type;
            $data['is_active'] = 1;
            DB::beginTransaction();
            $product->update($data);
            if($product){
                if($request->product_type=="single"){
                    $product_item = $request->only('product_name','product_form','product_strength','product_qty','price','xpedicare_url');
                    if($previous_product_type==$updated_product_type){
                        $productItem = ProductItem::where('product_id',$product->id)
                                    ->update($product_item);

                    } else {
                        $product_item['product_id'] = $product->id;
                        ProductItem::where('product_id',$product->id)
                                    ->delete();
                        $productItem = ProductItem::create($product_item);
                    }
                    if(!$productItem){
                        DB::rollBack();
                        return response()->json([
                            'status' => 400,
                            'message'=> trans('admin/errors.product_item_creation_error')
                        ]);
                    }
                } else if($request->product_type=="programs"){
                    $product_item_ids = [];
                    if($previous_product_type!=$updated_product_type){
                        ProductItem::where('product_id',$product->id)
                                    ->delete();
                    }
                    foreach ($request->product_items as $item) {
                        $product_item = Arr::only($item, [
                            'product_name',
                            'product_form',
                            'product_strength',
                            'product_qty',
                            'price',
                            'xpedicare_url',
                        ]);

                        if(@$item['id']!=''){
                            unset($product_item['product_id']);
                            $productItem = ProductItem::where('product_id',$product->id)->first();
                            if(!$productItem){
                                DB::rollBack();
                                return response()->json([
                                    'status' => 400,
                                    'message'=> trans('admin/errors.unable_to_find_product_item_error')
                                ]);
                            }
                            $product_item_ids[] = $productItem->id;
                            $productItem->update($product_item);
                        } else {
                            $product_item['product_id'] = $product->id;
                            $productItem = ProductItem::create($product_item);
                            $product_item_ids[] = $productItem->id;
                        }
                        if(!$productItem){
                            DB::rollBack();
                            return response()->json([
                                'status' => 400,
                                'message'=> trans('admin/errors.product_item_creation_error')
                            ]);
                        }
                    }
                    ProductItem::where('product_id',$product->id)
                                ->whereNotIn('id',$product_item_ids)
                                ->delete();
                }
            } else {
                DB::rollBack();
                return response()->json([
                    'status' => 400,
                    'message'=> trans('admin/errors.product_creation_failed')
                ]);
            }
            DB::commit();
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.update_product_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('Error while updating product : ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateProductImage(UpdateProductImageRequest $request){
        try {
            $product = Product::where('id',$request->id)->first();
            if($product){
                if($product->product_type=="single"){
                    $folder_name = $product->product_item->product_name;
                } else{
                    $folder_name = $product->product_title;
                }
                $path = self::FolderPathInfo($folder_name);
                $data['image'] = FileUploadTrait::UploadFile($request->image,$path,'admin');
                $product->update($data);
                return response()->json([
                    'status' => 200,
                    'message' => trans('admin/response.update_product_image_success')
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating product image : ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function DeleteProduct($id){
        try {
            $data = Product::where('id',$id)->first();
            if($data){
                $data->delete();
                return response()->json([
                    'status' => 200,
                    'message' => trans('admin/response.delete_product_success')
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while deleting product', ['Errors' => $th]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateProductStatus($id){
        try {
            $product = Product::select('id','is_active')
                    ->where('id',$id)
                    ->first();
            if($product){
                $product->update(['is_active'=>!$product->is_active]);
                $msg = ($product->is_active==0) ? trans('admin/response.product_is_active_status_inactive') : trans('admin/response.product_is_active_status_active');
                return response()->json([
                    'status' => 200,
                    'message' => $msg
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating category status (active/inactive) : ', ['Errors' => $th->getMessage()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateProductStockAvailabilityStatus($id){
        try {
            $product = Product::select('id','is_stock_available')
                    ->where('id',$id)
                    ->first();
            if($product){
                $product->update(['is_stock_available'=>!$product->is_stock_available]);
                $msg = trans('admin/response.product_stock_available_status_updated');
                return response()->json([
                    'status' => 200,
                    'message' => $msg
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating product stock availability status', ['Errors' => $th->getMessage()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public static function generateUniqueSlug($name,$id=null,$counter=0){
        $slug = Str::slug($name);
        if($counter>0){
            $slug = $slug.$counter;
        }
        $data = Product::where('slug',$slug);
        if($id){
            $data->whereNot('id',$id);
        }
        $data = $data->count();
        if($data>0){
            $counter = $counter + 1;
            return self::generateUniqueSlug($name,$id,$counter);
        } else {
            return $slug;
        }
    }

    private static function FolderPathInfo($category){
        $category = strtolower($category);
        $category = FileUploadTrait::RemoveSpaceAndSetUnderscore($category);
        return 'products/'.$category;
    }

    public function UpdateProductDescriptionFaqs(UpdateProductDescriptionFaqRequest $request){
        try {
            $product = Product::where('id',$request->id)->count();
            if($product==0){
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
            DB::beginTransaction();
            $product_desc_id = [];
            $product_faqs_id = [];
            if($request->product_descriptions && count($request->product_descriptions)>0){
                foreach ($request->product_descriptions as $description) {
                    if($description['title'] && $description['description']){
                        $description['product_id'] = $request->id;
                        $prod = ProductDescription::create($description);
                        $product_desc_id[] = $prod->id;
                    }
                }

            }
            if($request->product_faqs && count($request->product_faqs)>0){
                foreach ($request->product_faqs as $faq) {
                    if($faq['question'] && $faq['answer']){
                        $faq['product_id'] = $request->id;
                        $prod = ProductFaq::create($faq);
                        $product_faqs_id[] = $prod->id;
                    }
                }
            }
            ProductDescription::whereNotIn('id',$product_desc_id)->where('product_id',$request->id)->delete();
            ProductFaq::whereNotIn('id',$product_faqs_id)->where('product_id',$request->id)->delete();
            DB::commit();
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.product_detail_update_success')
            ]);
        } catch (\Throwable $th) {
            DB::rollBack();
            \Log::error('Error while updating product description and faqs', ['Errors' => $th->getMessage()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }
}
