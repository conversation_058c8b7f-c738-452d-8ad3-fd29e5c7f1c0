<?php

namespace App\Http\Controllers\Tenant\API\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use App\Http\Requests\API\Tenant\Admin\Profile\ChangePasswordRequest;
use App\Http\Requests\API\Tenant\Admin\Auth\{LoginRequest,SetupAccountPasswordRequest};
use App\Jobs\Tenant\AdminUserLogActivityJob;
use Jenssegers\Agent\Agent;
use App\Models\Tenant\User;
use App\Services\LocationService;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\RateLimiter;
use PragmaRX\Google2FA\Google2FA;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use App\Models\Tenant;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AuthController extends Controller
{
    /**
     * login
     * user login
     * @param  mixed $request
     * @return void
     */
    public function AdminLogin(LoginRequest $request){
        try {
            $user = User::where('email', $request->email)
                                    ->role('superadmin')
                                    ->first();
            if (empty($user)) {
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.invalid_credentials.message'),
                    'is_visible_forgot_password_btn' => 1
                ]);
            }
            $user_unique_value = $user->id.':'.request()->ip();
            if (RateLimiter::tooManyAttempts('admin-login:'.$user_unique_value, $perMinute = 5)) {
                $seconds = RateLimiter::availableIn('admin-login:'.$user_unique_value);
                return response()->json([
                    'status' => 400,
                    'message' => 'Your account has been temporarily blocked for '.$seconds.' seconds.'
                ]);
            }
            // dd(auth()->attempt($request->only(['email', 'password'])));
            if (!auth()->attempt($request->only(['email', 'password']))) {
                $total_count = RateLimiter::increment('admin-login:'.$user_unique_value);
                if($total_count>=5){
                    return response()->json([
                        'status' => 400,
                        'message' => 'Your account has been temporarily blocked for 60 seconds.'
                    ]);
                }
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.invalid_credentials.message'),
                    'is_visible_forgot_password_btn' => 1
                ]);
            }
            $is_2fa_verified = 1;
            if($user->two_factor_enabled==1){
                if(!empty($request->authentication_code)){
                    $google2fa = new Google2FA();
                    $secret = decrypt($user->two_factor_secret);
                    $valid = $google2fa->verifyKey($secret, $request->authentication_code);
                    $is_2fa_verified = ($valid) ? 1 : 0;
                } else{
                    // verify 2fa code
                    return response()->json([
                        'status' => 200,
                        '2fa_enabled' => 1
                    ]);
                }
            }
            // dd('s')
            if($is_2fa_verified==1){
                try {
                    $token = $user->createToken('admin-token');
                } catch (\Throwable $th) {
                    return response()->json([
                        'status' => 500,
                        'message' => trans('admin/errors.passport_token_error')
                    ]);
                }

                $agent = new Agent();

                $job['tenant'] = tenant(); // current tenant
                $job['user_id'] = $user->id;
                $job['token_id'] = $token->accessToken->id;
                $job['agent'] = $agent;
                $job['ip'] = LocationService::get_client_ip();
                // AdminUserLogActivityJob::dispatch($job);
                AdminUserLogActivityJob::dispatch($job);

                $encryptedToken = Crypt::encryptString($token->plainTextToken);
                RateLimiter::clear('admin-login:'.$user->id);
                $user_details['full_name'] = $user->full_name;
                $user_details['email'] = $user->email;
                $user_details['role'] = $user->getRoleNames()[0];
                return response()->json([
                    'status' => 200,
                    'userData' => $user_details
                ])->cookie(
                    'accessToken',
                    $encryptedToken,
                    60 * 72,       // 3 day
                    '/',
                    null,
                    true,          // Secure: only send over HTTPS
                    true,          // HttpOnly: inaccessible to JS
                    false,
                    'Strict'
                );
            } else {
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.invalid_2fa_code')
                ]);
            }
        } catch (\Throwable $e) {
            \Log::error('An error occurred in admin login api', [
                'message' => $e->getMessage(),
                'error' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public static function GenerateAuthToken($user) {
        $token_details = $user->createToken(env('APP_NAME'));
        $token['token'] = $token_details->accessToken;

        $token['id'] = $token_details->token->id;
        // $token_details->token->update(['user_timezone'=>$timezone_detail['timezone']]);
        // $token['expires_at'] = \Carbon\Carbon::parse($token_details->token->expires_at)->format('Y-m-d H:i:s');
        return $token;
    }

    public function SetupPassword(SetupAccountPasswordRequest $request) {
        try {
            try {
                $token = Crypt::decryptString($request->token);
            } catch (\Throwable $th) {
                $token = "";
            }
            $tenant = Tenant::where('id', tenant()->id)
                        ->where('invitation_token', $token)
                        ->first();
            if($tenant){
                $date = Carbon::parse($tenant->invitation_expired_at);
                if($date->isPast()){
                    return response()->json([
                        'status' => 400,
                        'message' => trans('admin/errors.link_expired'),
                        'linkExpired' => true
                    ]);
                }
                if($tenant->is_invitation_accepted == 0){
                    $tenant->update([
                        'is_invitation_accepted'=>1
                    ]);
                }
            } else {
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.invalid_token')
                ]);
            }

            $user = User::role('superadmin')
                        ->first();
            if (empty($user)) {
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.unable_to_find_user')
                ]);
            }
            $user->update(['password' => $request->password]);
            $user_details['full_name'] = $user->full_name;
            $user_details['email'] = $user->email;
            $user_details['role'] = $user->getRoleNames()[0];
            try {
                $token = $user->createToken('admin-token');
            } catch (\Throwable $th) {
                return response()->json([
                    'status' => 500,
                    'message' => trans('centraladmin/errors.sanctum_token_error')
                ]);
            }
            $encryptedToken = Crypt::encryptString($token->plainTextToken);
            return response()->json([
                'status' => 200,
                'userData' => $user_details
            ])->cookie(
                'accessToken',
                $encryptedToken,
                60 * 72,       // 3 day
                '/',
                null,
                true,          // Secure: only send over HTTPS
                true,          // HttpOnly: inaccessible to JS
                false,
                'Strict'
            );

        } catch (\Throwable $th) {
            Log::error('An error occurred in admin setup password api', [
                'message' => $th->getMessage(),
                'error' => $th->getTraceAsString()
            ]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function logout(Request $request)
    {
        $token_id = $request->header('accessTokenID');
        if($token_id){
            DB::table('personal_access_tokens')->where('id', $token_id  )->delete();
        }
        return  response()->json(['status' => 200])->cookie(
                        'accessToken',  // cookie name
                        '',             // empty value
                        -1,             // negative minutes = expire immediately
                        '/',            // path
                        null,           // domain (null = current domain)
                        true,           // secure
                        true,           // httpOnly
                        false,          // raw
                        'Strict'        // sameSite
                    );
    }

    public function VerifyToken() {
        return response()->json(['status' => 200]);
    }

    public function ChangePassword(ChangePasswordRequest $request) {
        $user = Auth::user();
        if (!Hash::check($request->current_password, $user->password)) {
            return response()->json([
                'status'=> 400,
                'message' => "The current password you entered is incorrect."
            ]);
        }
        $user->update([
            "password"=> $request->new_password
        ]);
        return response()->json([
            'status'=> 200,
            'message' => "Password changed successfully."
        ]);
    }

    public function VerifyAccountSetup($token) {
        // dd(tenant()->id);
        try {
            $token = Crypt::decryptString($token);
        } catch (\Throwable $th) {
            $token = "";
        }
        $tenant = Tenant::where('id', tenant()->id)
                        ->where('invitation_token', $token)
                        ->first();
        if (empty($tenant)) {
            return response()->json([
                'status' => 400,
                'message' => trans('admin/errors.invalid_token')
            ]);
        }
        if($tenant->is_invitation_accepted == 1){
            return response()->json([
                'status' => 400,
                'message' => trans('admin/errors.account_already_setup'),
                'isAuth' => 1
            ]);
        }
        $date = Carbon::parse($tenant->invitation_expired_at);
        if($date->isPast()){
            return response()->json([
                'status' => 400,
                'message' => trans('admin/errors.link_expired'),
                'linkExpired' => true
            ]);
        }
        return response()->json(['status' => 200]);
    }
}
