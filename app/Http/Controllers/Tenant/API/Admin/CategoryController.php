<?php

namespace App\Http\Controllers\Tenant\API\Admin;

use App\Http\Controllers\Controller;
use App\Traits\FileUploadTrait;
use Illuminate\Http\Request;
use App\Utils\PaginateCollection;
use App\Http\Requests\API\Tenant\Admin\Category\{AddCategoryRequest,EditCategoryRequest, UpdateCategoryImageRequest};
use App\Models\Tenant\{
    Product,
    SubCategory,
    MainCategory
};
use Illuminate\Support\Str;
use App\Traits\DateTrait;

class CategoryController extends Controller
{
    use DateTrait;

    public function AddCategory(AddCategoryRequest $request){
        try {
            $data = $request->only('name','gender','description');
            $validate_category = self::ValidateCategory($data);
            if($validate_category==false){
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.category_with_gender_already_exist')
                ]);
            }
            $slug = self::generateSlug($data['name']);
            $data['slug'] = $slug;
            $path = self::FolderPathInfo($data['name']);
            $data['image'] = FileUploadTrait::UploadFile($request->image,$path);
            $data['is_active'] = 1;
            MainCategory::create($data);
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.add_main_category_success')
            ]);
        } catch (\Throwable $th) {
            \Log::error('An error occurred while adding main category ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function ListCategory(Request $request){
        $perpage = (@$request->perPage) ? $request->perPage : 10;
        $categories = MainCategory::select('id','name','slug','gender','image','is_active','created_at','updated_at');

        if($request->is_active!=''){
            $categories->where('is_active',$request->is_active);
        }
        if($request->searchQuery!=''){
            $search = strtolower($request->searchQuery);
            $categories = $categories->where(function($q) use($search){
                $q->where('name', 'LIKE', "%" . $search . "%")
                    ->orWhere('slug', 'LIKE', "%" . $search . "%");
            });
        }

        if($request->sortByColumnName!='' && $request->isSortDirDesc!=''){
            $categories = $categories->orderBy($request->sortByColumnName,$request->isSortDirDesc);
        } else {
            $categories = $categories->latest();
        }

        $categories = $categories->paginate($perpage);
        $timezone = $this->GetUserTimezone($request);
        $categories->map(function ($categories) use($request,$timezone){
            $categories->created_date = $this->GetDate($categories->created_at,$timezone);
            $categories->created_time = $this->GetTime($categories->created_at,$timezone);
            $categories->updated_date = $this->GetDate($categories->updated_at,$timezone);
            $categories->updated_time = $this->GetTime($categories->updated_at,$timezone);
            if($categories->parent_id){
                $categories->parent_category_name = @$categories->parent_category->name;
            }
            unset($categories->created_at,$categories->parent_id,$categories->updated_at,$categories->parent_category);
            return $categories;
        });
        $info = PaginateCollection::paginate($categories);
        return response()->json([
            'status' =>200,
            'mainCategories' => $info
        ]);
    }

    public function ViewCategoryById($id){
        $category = MainCategory::select('id','name','slug','gender','image','is_active','description')
                            ->where('id',$id)
                            ->first();
        if($category){
            if($category->parent_id){
                $category->parent_category_name = @$category->parent_category->name;
            }
            unset($category->parent_category);
            return response()->json([
                'status' => 200,
                'mainCategory' => $category
            ]);
        } else {
            return response()->json([
                'status' =>404,
                'message' => trans('admin/errors.record_not_found')
            ]);
        }
    }

    public function EditCategory(EditCategoryRequest $request){
        try {
            $category = MainCategory::where('id',$request->id)->first();
            if($category){
                $data = $request->only('name','gender','description');
                $validate_category = self::ValidateCategory($data,$category->id);
                if($validate_category==false){
                    return response()->json([
                        'status' => 400,
                        'message' => trans('admin/errors.category_with_gender_already_exist')
                    ]);
                }
                $slug = self::generateSlug($data['name'],$category->id);
                $data['slug'] = $slug;
                $category->update($data);
                return response()->json([
                    'status' => 200,
                    'message' => trans('admin/response.update_main_category_success')
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating category : ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateMainCategoryImage(UpdateCategoryImageRequest $request){
        try {
            $category = MainCategory::where('id',$request->id)->first();
            if($category){
                $path = self::FolderPathInfo($category->name);
                $data['image'] = FileUploadTrait::UploadFile($request->image,$path);
                $category->update($data);
                return response()->json([
                    'status' => 200,
                    'message' => trans('admin/response.update_main_category_image_success')
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating category : ', ['message' => $th->getMessage(), 'error' => $th->getTraceAsString()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function DeleteCategory($id){
        try {
            $data = MainCategory::where('id',$id)->first();
            if($data){
                $check_sub_category = SubCategory::where('main_category_id',$id)->count();
                if($check_sub_category>0){
                    return response()->json([
                        'status' => 400,
                        'message' => trans('admin/errors.category_has_sub_category')
                    ]);
                }

                $check_product = Product::where('main_category_id',$id)->count();
                if($check_product>0){
                    return response()->json([
                        'status' => 400,
                        'message' => trans('admin/errors.category_has_product')
                    ]);
                }
                $data->delete();
                return response()->json([
                    'status' => 200,
                    'message' => trans('admin/response.delete_main_category_success')
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while deleting main category : ', ['Errors' => $th]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function UpdateCategoryStatus($id){
        try {
            $category = MainCategory::select('id','is_active')
                    ->where('id',$id)
                    ->first();
            if($category){
                $category->update(['is_active'=>!$category->is_active]);
                $msg = ($category->is_active==0) ? trans('admin/response.main_category_is_active_status_inactive') : trans('admin/response.main_category_is_active_status_active');
                return response()->json([
                    'status' => 200,
                    'message' => $msg
                ]);
            } else {
                return response()->json([
                    'status' =>404,
                    'message' => trans('admin/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            \Log::error('Error while updating category status (active/inactive) : ', ['Errors' => $th->getMessage()]);
            return response()->json([
                'status' => 500,
                'message' => trans('admin/errors.server_error')
            ]);
        }
    }

    public function MainCategoryList(){
        $categories = MainCategory::select('id','name','gender')
                                ->where('is_active',1)
                                ->latest()
                                ->get();
        return response()->json([
            'status' => 200,
            'mainCategories' => $categories
        ]);
    }

    private static function FolderPathInfo($category){
        $category = strtolower($category);
        $category = FileUploadTrait::RemoveSpaceAndSetUnderscore($category);
        return 'categories/'.$category;
    }

    private static function generateSlug($name,$id=null,$counter=0){
        $slug = Str::slug($name);
        if($counter>0){
            $slug = $slug.$counter;
        }
        $data = MainCategory::where('slug',$slug);
        if($id){
            $data->whereNot('id',$id);
        }
        $data = $data->count();
        if($data>0){
            $counter = $counter + 1;
            return self::generateSlug($name,$id,$counter);
        } else {
            return $slug;
        }
    }

    private static function ValidateCategory($data,$id=null){
        $exists = MainCategory::where('name', $data['name'])
            ->where(function ($q) use ($data) {
                if (is_null($data['gender'])) {
                    // If inserting null → conflict if male/female exists
                    $q->whereNotNull('gender');
                } else {
                    // If inserting male/female → conflict if null exists
                    $q->whereNull('gender');
                }
            });
        if($id){
            $exists->where('id','!=',$id);
        }
        $exists = $exists->exists();
        if($exists){
            return false;
        } else {
            return true;
        }
    }
}
