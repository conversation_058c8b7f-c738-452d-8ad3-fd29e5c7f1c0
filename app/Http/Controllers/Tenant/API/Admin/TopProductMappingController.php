<?php

namespace App\Http\Controllers\Tenant\API\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Tenant\{Product,TopProductMapping};
use App\Traits\DateTrait;

class TopProductMappingController extends Controller
{
    use DateTrait;

    public function AllProducts(){
        $products = Product::select(
                    'products.id',
                    'products.product_title',
                    'products.product_type',
                    'main_categories.name as main_category_name',
                    'sub_categories.name as sub_category_name'
                )
                ->with('product_item',function($q){
                    $q->select('product_id','product_name','product_form')
                        ->orderBy('price','asc');
                })
                ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                ->where('products.is_active',1)
                ->where('main_categories.is_active',1)
                ->orderBy('products.created_at','desc')
                ->get();
                // dd($products);
        $products->map(function($products){
            if($products->product_type=='single'){
                $title = $products->product_item->product_name;
            } else {
                $title = $products->product_title;
            }
            $products->product_name_title = $title;
            $products->product_form = $products->product_item->product_form;
            unset($products->product_item,$products->product_type,$products->product_title);
        });
        return response()->json([
            'status' => 200,
            'allProducts' => $products
        ]);
    }

    public function AddTopProduct($id){
        $product = Product::where('id',$id)->where('is_active',1)->count();
        if($product>0){
            $product = TopProductMapping::where('product_id',$id)->count();
            if($product>0){
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.product_already_mapped')
                ]);
            }
            $countTopProduct = TopProductMapping::count();
            if($countTopProduct>=6){
                return response()->json([
                    'status' => 400,
                    'message' => trans('admin/errors.top_product_limit')
                ]);
            }
            TopProductMapping::create(['product_id'=>$id]);
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.top_product_mapping_success')
            ]);
        } else {
            return response()->json([
                'status' => 400,
                'message' => trans('admin/errors.record_not_found')
            ]);
        }
    }

    public function ListTopProduct(){
        $products = TopProductMapping::select(
                            'top_product_mappings.id as top_product_id',
                            'top_product_mappings.product_id',
                            'products.id',
                            'products.product_title',
                            'products.product_type',
                            'main_categories.name as main_category_name'
                        )
                        ->with('product.product_item',function($q){
                                $q->select('product_id','product_name','product_form')
                                    ->orderBy('price','asc');
                        })
                        ->where('products.is_active',1)
                        ->where('main_categories.is_active',1)
                        ->join('products', 'top_product_mappings.product_id', '=', 'products.id')
                        ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                        ->orderBy('top_product_mappings.product_index','asc')
                        ->get();
        $products->map(function($products){
            if($products->product->product_type=='single'){
                $title = $products->product->product_item->product_name;
            } else {
                $title = $products->product->product_title;
            }
            $products->product_name_title = $title;
            $products->image = $products->product->image;
            unset($products->product,$products->id,$products->product_title);
        });
        return response()->json([
            'status' => 200,
            'topProducts' => $products
        ]);
    }

    public function DeleteTopProduct($id){
        $product = TopProductMapping::where('id',$id)->first();
        if($product){
            $product->delete();
            return response()->json([
                'status' => 200,
                'message' => trans('admin/response.top_product_unmapping_success')
            ]);
        } else {
            return response()->json([
                'status' => 400,
                'message' => trans('admin/errors.record_not_found')
            ]);
        }
    }

    public function ReOrderTopProduct(Request $request){
        $ids = $request->ids;
        if(empty($ids) || count($ids)<1){
            return response()->json([
                'status' => 400,
                'message' => trans('admin/errors.id_field_required')
            ]);
        }
        foreach ($ids as $key => $id) {
            TopProductMapping::where('id',$id)->update(['product_index'=>$key]);
        }
        return response()->json([
            'status' => 200,
            'message' => trans('admin/response.top_product_reorder_success')
        ]);
    }
}
