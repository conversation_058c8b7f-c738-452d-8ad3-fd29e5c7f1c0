<?php

namespace App\Http\Controllers\Tenant\API\User;

use App;
use App\Http\Controllers\Controller;
use App\Models\Tenant\AppSetting;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;

class AppSettingController extends Controller
{

    public function AppSettings(){
        $setting = DB::table('app_settings')
                    ->select('app_name','app_logo','app_favicon','lp_data')
                    ->first();
        if($setting){
            $appSetting =new AppSetting();
            if($setting->app_logo==null){
                if(env('APP_ENV')=='local'){
                    $setting->app_logo= 'https://i.postimg.cc/PqMJkpmq/demo-logo.png';
                } else {
                    $setting->app_logo= config('filesystems.disks.s3.url').'/public-assets/demo-company-logo.png';
                }
            } else {
                $setting->app_logo = $appSetting->getAppLogoAttribute($setting->app_logo);
            }
            if($setting->app_favicon!=null){
                $setting->app_favicon = $appSetting->getAppFaviconAttribute($setting->app_favicon);
            }
            if($setting->lp_data){
                $appConfig = $appSetting->getLpDataAttribute($setting->lp_data);
                // print_r($appConfig['heroCallout']);
                // exit;
                if(@$appConfig['heroCallout']['backgroundImage']!=''){
                    $appConfig['heroCallout']['backgroundImage'] = $appSetting->getImages($appConfig['heroCallout']['backgroundImage']);
                }
                if($appConfig['fixItTogether']['cards'] && count($appConfig['fixItTogether']['cards'])>0){
                    foreach($appConfig['fixItTogether']['cards'] as &$card){
                        if(@$card['backgroundImage']!=''){
                            $card['backgroundImage'] = $appSetting->getImages($card['backgroundImage']);
                        }
                    }
                }
                $setting->lp_data = $appConfig;
            } else {
                $filePath = app_path('Constants/frontend-config.json');
                if (!File::exists($filePath)) {
                    // Create file with empty JSON object
                    File::put($filePath, '{}');
                }

                $json = File::get($filePath);
                $data = json_decode($json, true);
                $setting->lp_data = $data;
            }
        } else {
            $setting = (object)[
                'app_name'=> 'Your App Name',
                'app_logo'=> (env('APP_ENV')=='local') ? 'https://i.postimg.cc/PqMJkpmq/demo-logo.png' : config('filesystems.disks.s3.url').'/demo-logo.png',
            ];
        }
        return response()->json([
            "status"=> 200,
            "app_settings"=>$setting
        ]);
    }
}
