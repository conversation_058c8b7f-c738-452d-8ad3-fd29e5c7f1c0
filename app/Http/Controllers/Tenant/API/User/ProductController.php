<?php

namespace App\Http\Controllers\Tenant\API\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\Tenant\Admin\ProductLead\AddUserProductLeadRequest;
use App\Models\Tenant\{MainCategory, Product, ProductDescription, ProductFaq, ProductItem, TopProductMapping, UserProductLead};
use App\Models\UserProductLead as CentralUserProductLead;
use App\Utils\PaginateCollection;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ProductController extends Controller
{

    public function GetAllCategories(){
        $categories = MainCategory::select('id','name','slug','gender','image','description')
                        ->whereHas('products', function ($q) {
                            $q->where('is_active', 1)
                            ->where(function($query) {
                                $query->whereNull('sub_category_id') // products without subcategory
                                        ->orWhereHas('sub_category', function($sub) {
                                            $sub->where('is_active', 1);
                                        });
                            });
                        })
                        ->with([
                            'sub_categories' => function ($query) {
                                $query->select('id','main_category_id','name','slug')
                                    ->where('is_active', 1)
                                    ->whereHas('products', function ($q) {
                                        $q->where('is_active', 1);
                                    });
                            }
                        ])
                        ->where('is_active', 1)
                        ->get();
        return response()->json([
            "status"=> 200,
            "categories"=>$categories
        ]);
    }

    public function FetchLandingPageData(){
        $categories = MainCategory::select('id','name','slug','gender','image','description')
                        ->whereHas('products', function ($q) {
                            $q->where('is_active', 1)
                            ->where(function($query) {
                                $query->whereNull('sub_category_id') // products without subcategory
                                        ->orWhereHas('sub_category', function($sub) {
                                            $sub->where('is_active', 1);
                                        });
                            });
                        })
                        ->where('is_active', 1)
                        ->get();

        $products = TopProductMapping::select(
                        'top_product_mappings.product_id',
                        'products.product_title',
                        'products.slug',
                        'products.product_type',
                        'main_categories.name as main_category_name',
                        'sub_categories.name as sub_category_name'
                    )

                    ->with(['product' => function($q) {
                        $q->with([
                            'product_items' => function ($q3) {
                                $q3->select('product_id','id as product_item_id','product_name','product_form','price','product_strength','product_qty','xpedicare_url');
                            },
                            'product_item' => function ($q2) {
                                $q2->select('product_id','id','price','product_name','product_form','product_strength','xpedicare_url')->orderBy('price', 'asc');
                            }
                        ]);
                    }])
                    ->whereHas('product.product_items')
                    ->where('products.is_active',1)
                    ->where('main_categories.is_active',1)
                    ->where(function($query) {
                        $query->whereNull('products.sub_category_id')
                            ->orWhere('sub_categories.is_active', 1);
                    })
                    ->join('products', 'top_product_mappings.product_id', '=', 'products.id')
                    ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                    ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                    ->orderBy('top_product_mappings.product_index','asc')
                    ->get();
        $products->map(function($products){
            $product_items = $products->product->product_item;
            $products->image = $products->product->image;
            $products->price = $product_items->price;
            if($products->product->product_type=='single'){
                $products->product_item_id = $product_items->id;
                $products->product_name_title = $product_items->product_name;
                $products->product_form = $product_items->product_form;
                $products->product_strength = $product_items->product_strength;
                $products->product_qty = $product_items->product_qty;
                $products->xpedicare_url = $product_items->xpedicare_url;
            } else {
                $products->product_name_title = $products->product->product_title;
                $products->product_items = $products->product->product_items;
                unset($product_items->id);
            }
            unset($products->product_id,$products->product,$products->product_title);
        });
        $data = [
            'categories' => $categories,
            'topProducts' => $products
        ];
        return response()->json([
            'status' => 200,
            'data' => $data
        ]);
    }

    public function GetHeaderCategories(){
        $mainCategories = MainCategory::select('id','name','gender','slug','image')
                            ->where('is_active', 1)
                            // only main categories that have valid products
                            ->whereHas('products', function ($q) {
                                $q->where('is_active', 1)
                                ->where(function($qq) {
                                    $qq->whereNull('products.sub_category_id') // no subcategory
                                        ->orWhereHas('sub_category', function($sq) {
                                            $sq->where('is_active', 1); // only active subcategories
                                        });
                                });
                            })
                            ->with([
                                // only active subcategories that have valid products
                                'sub_categories' => function ($sub) {
                                    $sub->select('id','main_category_id','name','slug')
                                        ->where('is_active', 1)
                                        ->whereHas('products', function ($q) {
                                            $q->where('is_active', 1)
                                            ->whereHas('main_category', function($mc) {
                                                $mc->where('is_active', 1);
                                            });
                                        });
                                },
                                // products inside main category
                                'products' => function ($prod) {
                                    $prod->select(
                                            'products.id',
                                            'products.main_category_id',
                                            'products.sub_category_id',
                                            'products.product_title',
                                            'products.slug',
                                            'products.product_type',
                                            'products.image',
                                            'main_categories.name as main_category_name',
                                            'sub_categories.name as sub_category_name'
                                        )
                                        ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                                        ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                                        ->where('products.is_active', 1)
                                        ->where(function($query) {
                                            $query->whereNull('products.sub_category_id')   // no subcategory
                                                ->orWhere('sub_categories.is_active', 1); // or active subcategory
                                        })
                                        ->with([
                                            'product_item' => fn($q) => $q->select(
                                                'product_id','id as product_item_id','price','product_name','product_form','product_strength','xpedicare_url'
                                            )->orderBy('price','asc'),
                                            'product_items' => fn($q) => $q->select(
                                                'product_id','id as product_item_id','product_name','product_form','product_strength','product_qty','price','xpedicare_url'
                                            )
                                        ])
                                        ->inRandomOrder();
                                }
                            ])
                            ->get();


        if(count($mainCategories)>0){
            $mainCategories->map(function ($mainCategory) {

                // Loop through products directly under main category
                $mainCategory->products->map(function ($product) {
                    if ($product->product_type === 'single') {
                        $product->product_item_id = $product->product_item->product_item_id ?? null;
                        $product->xpedicare_url   = $product->product_item->xpedicare_url ?? null;
                        $product->product_name_title = $product->product_item->product_name ?? $product->product_title;
                        $product->product_form   = $product->product_item->product_form ?? null;
                        $product->product_strength = $product->product_item->product_strength ?? null;
                        $product->product_qty    = $product->product_item->product_qty ?? null;
                        $product->price          = $product->product_item->price ?? null;
                        unset($product->product_items);
                    } else {
                        $productItem             = $product->product_item;
                        $product->product_name_title = $product->product_title;
                        // $product->product_item_id = $productItem->product_item_id ?? null;
                        $product->product_form   = $productItem->product_form ?? null;
                        $product->product_strength = $productItem->product_strength ?? null;
                        $product->product_qty    = $productItem->product_qty ?? null;
                        $product->price          = $productItem->price ?? null;
                    }

                    unset($product->product_item,$product->id,$product->sub_category_id,$product->main_category_id,$product->product_title);
                    return $product;
                });

                return $mainCategory;
            });
            $products = TopProductMapping::select(
                        'top_product_mappings.product_id',
                        'products.product_title',
                        'products.slug',
                        'products.product_type',
                        'main_categories.name as main_category_name',
                        'sub_categories.name as sub_category_name'
                    )

                    ->with(['product' => function($q) {
                        $q->with([
                            'product_items' => function ($q3) {
                                $q3->select('product_id','product_name','product_form','price','product_strength','product_qty','xpedicare_url');
                            },
                            'product_item' => function ($q2) {
                                $q2->select('product_id','price','product_name','product_form','product_strength','xpedicare_url')->orderBy('price', 'asc');
                            }
                        ]);
                    }])
                    ->whereHas('product.product_items')
                    ->where('products.is_active',1)
                    ->where('main_categories.is_active',1)
                    ->where(function($query) {
                        $query->whereNull('products.sub_category_id')   // no subcategory
                            ->orWhere('sub_categories.is_active', 1); // or active subcategory
                    })
                    ->join('products', 'top_product_mappings.product_id', '=', 'products.id')
                    ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                    ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                    ->orderBy('top_product_mappings.product_index','asc')
                    ->get();
            $products->map(function($products){
                $product_items = $products->product->product_item;
                $products->image = $products->product->image;
                $products->price = $product_items->price;
                if($products->product->product_type=='single'){
                    $products->product_name_title = $product_items->product_name;
                    $products->product_form = $product_items->product_form;
                    $products->product_strength = $product_items->product_strength;
                    $products->product_qty = $product_items->product_qty;
                    $products->xpedicare_url = $product_items->xpedicare_url;
                } else {
                    $products->product_name_title = $products->product->product_title;
                    $products->product_items = $products->product->product_items;
                }
                unset($products->product_id,$products->product,$products->product_title);
            });
            return response()->json([
                "status"=> 200,
                "mainCategories"=>$mainCategories,
                "topProducts"=>$products
            ]);
        } else {
            return response()->json([
                "status"=> 200,
                "mainCategories"=>[],
                "topProducts"=>[]
            ]);
        }
    }

    public function GetProducts(Request $request){
        $perpage = ($request->perPage) ? $request->perPage : 10;
        $products = Product::select(
                    'products.id',
                    'products.product_title',
                    'products.slug',
                    'products.product_type',
                    'products.image',
                    'main_categories.name as main_category_name',
                    'sub_categories.name as sub_category_name'
                )
                ->with('product_item',function($q){
                    $q->select('product_id','id as product_item_id','price','product_name','product_form','product_strength','xpedicare_url')->orderBy('price','asc');
                })
                ->with('product_items',function($q){
                    $q->select('product_id','id as product_item_id','product_name','product_form','product_strength','product_qty','price','xpedicare_url');
                })
                ->where(function($query) {
                    $query->whereNull('products.sub_category_id')   // no subcategory
                          ->orWhere('sub_categories.is_active', 1); // or active subcategory
                })
                ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                ->where('products.is_active',1)
                ->where('main_categories.is_active',1);
        if($request->searchQuery!=''){
            $search = strtolower($request->searchQuery);
            $products = $products->where(function($q) use($search){
                $q->where('products.product_title', 'LIKE', "%" . $search . "%")
                    ->orWhere('products.slug', 'LIKE', "%" . $search . "%")
                    ->orwhereHas('product_item',function($q) use($search){
                        $q->where('product_name', 'LIKE', "%" . $search . "%");
                    });
            });
        }
        if($request->slug!=''){
            $products = $products->where(function($q)use($request){
                $q->where(function($q)use($request){
                    $q->where('main_categories.slug', $request->slug)
                        ->orWhere('main_categories.slug', $request->slug);
                })->orWhere(function($q)use($request){
                    $q->where('sub_categories.slug', $request->slug)
                        ->orWhere('sub_categories.slug', $request->slug);
                });
            });
        }
        if($request->gender!=''){
            $products = $products->where(function($q)use($request){
                $q->where('main_categories.gender', $request->gender)
                    ->orWhereNull('main_categories.gender');
            });
        }
        $products = $products->orderBy('products.created_at','desc')->paginate($perpage);
        $products->map(function ($products) use($request){
            $product_items = $products->product_item;
            $products->product_form = $product_items->product_form;

            $products->product_strength = $product_items->product_strength;
            $products->product_qty = $product_items->product_qty;
            $products->price = $product_items->price;
            if($products->product_type=='single'){
                $title = $product_items->product_name;
                $products->product_item_id = $product_items->product_item_id;
                $products->xpedicare_url = $product_items->xpedicare_url;
                unset($products->product_items);
            } else {
                $title = $products->product_title;
            }
            $products->product_name_title = $title;

            unset($products->product_title,$products->id,$products->product_item);
            return $products;
        });
        $info = PaginateCollection::paginate($products);
        return response()->json([
            "status"=> 200,
            "products"=>$info
        ]);
    }

    public function ProductDetails($slug){
        $product = Product::select(
                    'products.id',
                    'products.product_title',
                    'products.main_category_id',
                    'products.sub_category_id',
                    'products.slug',
                    'products.product_type',
                    'products.image',
                    'main_categories.name as main_category_name',
                    'main_categories.slug as main_category_slug',
                    'sub_categories.name as sub_category_name'
                )
                ->with('product_item',function($q){
                    $q->select('product_id','id as product_item_id','price','product_name','product_form','product_strength','xpedicare_url')->orderBy('price','asc');
                })
                ->with('product_items',function($q){
                    $q->select('product_id','id as product_item_id','product_name','product_form','product_strength','product_qty','price','xpedicare_url');
                })
                ->where(function($query) {
                    $query->whereNull('products.sub_category_id')   // no subcategory
                          ->orWhere('sub_categories.is_active', 1); // or active subcategory
                })
                ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                ->where('products.is_active',1)
                ->where('products.slug',$slug)
                ->first();
        if($product){
            $product->price = $product->product_item->price;
            $product_items = $product->product_item;
            $product->product_form = $product_items->product_form;
            $product->product_strength = $product_items->product_strength;
            $product->product_qty = $product_items->product_qty;
            if($product->product_type=='single'){
                $product->product_item_id = $product_items->product_item_id;
                $title = $product_items->product_name;
                $product->xpedicare_url = $product_items->xpedicare_url;
                unset($product->product_items);
            } else {
                $title = $product->product_title;
            }
            $product->product_name_title = $title;
            $recommendProducts = self::getSuggestProducts($product->id,$product->main_category_id,$product->sub_category_id);
            $product->recommendProducts = $recommendProducts;
            $product->product_descriptions = ProductDescription::select('title','description')->where('product_id',$product->id)->get();
            $product->product_faqs = ProductFaq::select('question','answer')->where('product_id',$product->id)->get();
            unset($product->product_item,$product->main_category_id,$product->sub_category_id,$product->id,$product->product_title);
            return response()->json([
                "status"=> 200,
                "productDetails"=>$product
            ]);
        } else {
            return response()->json([
                "status"=> 400,
                "productDetails"=>null
            ]);
        }

    }

    public function CollectUserProductLead(AddUserProductLeadRequest $request){
        try {
            $data = $request->only('name','email','phone_number');
            $product = ProductItem::select(
                'product_items.id',
                'product_items.product_id',
                'product_items.product_name',
                'product_items.product_form',
                'product_items.product_strength',
                'product_items.product_qty',
                'product_items.price',
                'product_items.xpedicare_url',
                'products.main_category_id',
                'products.sub_category_id',
                'products.slug',
                'products.product_type',
                'products.product_title',
                'products.image',
                'main_categories.name as main_category_name',
                'sub_categories.name as sub_category_name'
            )
                ->join('products', 'product_items.product_id', '=', 'products.id')
                ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
                ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
                ->where('product_items.id', $request->product_item_id)
                ->whereHas('product', function ($q) {
                    $q->where('is_active', 1);
                })
                ->first();
            if($product){
                $product_details['product_id'] = $product->product_id;
                $product_details['product_item_id'] = $product->id;
                $product_details['main_category_name'] = $product->main_category_name;
                $product_details['sub_category_name'] = $product->sub_category_name;
                $product_details['product_type'] = $product->product_type;
                $product_details['product_form'] = $product->product_form;
                $product_details['product_strength'] = $product->product_strength;
                $product_details['product_qty'] = $product->product_qty;
                $product_details['price'] = $product->price;
                $product_details['xpedicare_url'] = $product->xpedicare_url;
                $product_details['image'] = $product->image;
                $product_details['slug'] = $product->slug;
                if($product->product_type=='single'){
                    $product_details['product_name'] = $product->product_name;
                } else {
                    $product_details['product_name'] = $product->product_name;
                    $product_details['product_title'] = $product->product_title;

                }
                $data['product_details'] = json_encode($product_details,JSON_UNESCAPED_SLASHES);
                $data['product_item_id'] = $product->id;
                $tenant_id = tenant()->id;
                $tenant_result = UserProductLead::create($data);
                if($tenant_result){
                    $data['tenant_id'] = $tenant_id;
                    unset($data['product_item_id']);
                    CentralUserProductLead::on('central')->create($data);
                    return response()->json([
                        "status"=> 200,
                        "message" => trans('api/response.user_product_lead_success')
                    ]);
                }

            } else {
                return response()->json([
                    "status"=> 400,
                    "message" => trans('api/errors.record_not_found')
                ]);
            }
        } catch (\Throwable $th) {
            Log::error('Error while collecting user product lead', [
                'Product Lead Data' => $request->all(),
                'Errors' => $th
            ]);
            return response()->json([
                "status"=> 400,
                "message" => trans('api/errors.server_error')
            ]);
        }
    }

    protected static function getSuggestProducts($id,$mainCategoryId,$subCategoryId=null){
        $baseQuery = Product::select(
            'products.id',
            'products.product_title',
            'products.main_category_id',
            'products.sub_category_id',
            'products.slug',
            'products.product_type',
            'products.image',
            'main_categories.name as main_category_name',
            'sub_categories.name as sub_category_name'
        )
        ->with('product_item',function($q){
            $q->select('product_id','id as product_item_id','price','product_name','product_form','product_strength','xpedicare_url')->orderBy('price','asc');
        })
        ->with(['product_items' => function($q) {
            $q->select('product_id','id as product_item_id','product_name','product_form','product_strength','product_qty','price','xpedicare_url');
        }])
        ->where(function($query) {
            $query->whereNull('products.sub_category_id')   // no subcategory
                    ->orWhere('sub_categories.is_active', 1); // or active subcategory
        })
        ->join('main_categories', 'products.main_category_id', '=', 'main_categories.id')
        ->leftJoin('sub_categories', 'products.sub_category_id', '=', 'sub_categories.id')
        ->where('products.is_active', 1)
        ->whereNot('products.id', $id);

        // Try subcategory first, fallback to main category
        $products = (clone $baseQuery)
            ->when($subCategoryId, fn($q) => $q->where('products.sub_category_id', $subCategoryId))
            ->when(!$subCategoryId, fn($q) => $q->where('products.main_category_id', $mainCategoryId))
            ->inRandomOrder()
            ->limit(6)
            ->get();

        // If no subcategory products, fallback to main category
        if ($products->isEmpty() && $subCategoryId) {
            $products = (clone $baseQuery)
                ->where('products.main_category_id', $mainCategoryId)
                ->inRandomOrder()
                ->limit(3)
                ->get();
        }
        // Transform collection
        return $products->map(function ($product) {
            $title = $product->product_type === 'single'
                ? optional($product->product_item)->product_name
                : $product->product_title;

            $prod = [
                'slug'                => $product->slug,
                // 'main_category_id'    => $product->main_category_id,
                // 'sub_category_id'     => $product->sub_category_id,
                'main_category_name'  => $product->main_category_name,
                'sub_category_name'   => $product->sub_category_name,
                'image'               => $product->image,
                'product_type'        => $product->product_type,
                'product_name_title'  => $title,
                'product_form'        => optional($product->product_item)->product_form,
                'product_strength'    => optional($product->product_item)->product_strength,
                'product_qty'         => optional($product->product_item)->product_qty,
                'price'       => optional($product->product_item)->price,
            ];
            if($product->product_type=='single'){
                $prod['product_item_id'] = optional($product->product_item)->product_item_id;
                $prod['xpedicare_url'] = optional($product->product_item)->xpedicare_url;
            } else {
                $prod['product_items'] = $product->product_items;
            }
            return (object)$prod;
        });
    }
}
