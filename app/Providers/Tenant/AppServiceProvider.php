<?php

namespace App\Providers\Tenant;

use Illuminate\Support\ServiceProvider;
use Carbon\CarbonInterval;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Str;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {

        Permission::retrieved(function (Permission $permission) {
            $permission->incrementing = false;
        });

        Permission::creating(function (Permission $permission) {
            $permission->incrementing = false;
            $permission->id = Str::uuid()->toString();
        });

        Role::retrieved(function (Role $role) {
            $role->incrementing = false;
        });

        Role::creating(function (Role $role) {
            $role->incrementing = false;
            $role->id = Str::uuid()->toString();
        });

        Gate::before(function ($user, $ability) {
            if ($user->hasRole('superadmin') || $user->hasRole('admin')) {
                return true;
            }
        });
    }
}
