<?php

namespace App\Utils;

use Illuminate\Container\Container;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Collection;

class PaginateCollection
{
    public static function paginate($results){
        $results = $results->toarray();
        $results['totalPage'] = (count($results['data'])>0) ? $results['last_page'] : 0;
        $results['totalRecords'] = (count($results['data'])>0) ? $results['total'] : 0;
        $results['records'] = $results['data'];
        unset($results['links'],$results['first_page_url'],$results['from'],$results['last_page'],$results['last_page_url'],$results['links'],$results['path'],$results['prev_page_url'],$results['next_page_url'],$results['last_page'],$results['total'],$results['to'],$results['data']);
        return $results;

    }
}
