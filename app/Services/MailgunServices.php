<?php
namespace App\Services;

use Illuminate\Support\Facades\Log;

class MailgunServices {

	public static function MailSend($email_data){
		try{
            $from_email_address = (@$email_data['from_email']!='') ? $email_data['from_email'] : config('services.mailgun.from_email_address');
            $apiUrl = rtrim(config('services.mailgun.api_url'), '/');
            $domainName = trim(config('services.mailgun.domain_name'));
            $apiKey = config('services.mailgun.api_key');
            $replyTo = config('services.mailgun.reply_to');
            $mail_data = [
                'template'   => $email_data['template_name'],
                't:variables' => json_encode($email_data['template_values']),
                'from'    => $from_email_address,
                'to' => $email_data['to_email'],
                'subject' => $email_data['subject'],
                'h:Reply-To' => $replyTo,
                'o:require-tls' => ''
            ];
            $url = "{$apiUrl}/{$domainName}/messages";
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $mail_data,
                CURLOPT_HTTPAUTH => CURLAUTH_BASIC,
                CURLOPT_USERPWD => "api:$apiKey",
            ));
            $response = curl_exec($curl);
            curl_close($curl);
            $response = json_decode($response, true);
            if(@$response['id']!=''){
                return true;
            } else {
                Log::error('Unable to send email to the user.',[
                    "Request" => $mail_data,
                    "ErrorsResponse" => $response
                ]);
                return false;
            }
            return true;
		} catch (\Throwable $th) {
		    Log::error('An error occurred when the email was sent to the user.',['Error'=>$th->getMessage(),'Errors'=>$th]);
			return false;
		}

	}
}
