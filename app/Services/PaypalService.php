<?php
namespace App\Services;

use App\Models\UserPaypalCardDetail;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
class PaypalService {

    public static function createVaultToken($post_data)
    {
        try {
            $provider = new PayPalClient;
            $provider->setApiCredentials(config('paypal'));
            $paypalToken = $provider->getAccessToken();
            $request_id = 'add-new-card-'.time();
            $response = $provider->setRequestHeader('PayPal-Request-Id', \Str::uuid())
                                 ->createPaymentSetupToken($post_data);
            return $response;
        } catch (\Throwable $th) {
            \Log::error('Unable to creating product on paypal',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return null;
        }
    }

    public static function createVaultPaymentToken($post_data)
    {
        try {
            $provider = new PayPalClient;
            $provider->setApiCredentials(config('paypal'));
            $provider->getAccessToken();
            $request_id = 'add-new-payment-token-'.time();
            $response = $provider->setRequestHeader('PayPal-Request-Id', \Str::uuid())
                                 ->createPaymentSourceToken($post_data);
            return $response;
        } catch (\Throwable $th) {
            \Log::error('Unable to creating vault payment token',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return null;
        }
    }

    public static function authorizePayment($paypal_vault_token,$plan_amount,$plan){
        $items = [
            [
                "sku" => "1",
                "name" => $plan->plan_title,
                "description" => $plan->summary,
                "quantity" => 1,
                "unit_amount" => [
                    "currency_code" => "USD",
                    "value" => $plan->monthly_price
                ],
                "image" => $plan->image
            ]
        ];

        // Only add setup fee if it exists and is greater than 0
        if (!empty($plan->setup_fee) && $plan->setup_fee > 0) {
            $items[] = [
                "sku" => "2",
                "name" => "Setup Fee",
                "description" => "A one-time setup fee applies to this plan.",
                "quantity" => 1,
                "unit_amount" => [
                    "currency_code" => "USD",
                    "value" => $plan->setup_fee
                ]
            ];
        }

        $payment = [
            'intent' => 'AUTHORIZE',
            'purchase_units' => [
                [
                    'amount' => [
                        "currency_code" => "USD",
                        "value" => $plan_amount,
                        "breakdown" => [
                            "item_total" => [
                                "currency_code" => "USD",
                                "value" => $plan_amount
                            ]
                        ]
                    ],
                    'items' => $items
                ]
            ],
            'payment_source' => [
                "card" => [
                    "vault_id" => $paypal_vault_token
                ]
            ]
        ];
        // dd($payment);
        $provider = new PayPalClient;
        $provider->setApiCredentials(config('paypal'));
        $provider->getAccessToken();
        $request_id = 'capture-plan-payment'.time().rand(10000,999999);
        $response = $provider->setRequestHeader('PayPal-Request-Id', $request_id)
                                ->createOrder($payment);
        if($response && @$response['status'] && @$response['status']=='COMPLETED' && @$response['purchase_units'] && !empty(@$response['id'])){
            return array(
                'status' => 200,
                'paymentInfo' => $response
            );
        } else {
            return array(
                'status' => 400,
                'paymentInfo' => $response
            );
        }
    }

    public static function CaptureAuthorizePayment($refill,$authorize_payment_id){
        $provider = new PayPalClient;
        $provider->setApiCredentials(config('paypal'));
        $provider->getAccessToken();
        $request_id = 'capture-authorize-payment'.time().rand(10000,999999);
        $response = $provider->setRequestHeader('PayPal-Request-Id', $request_id)
                                ->captureAuthorizedPayment($authorize_payment_id,"",$refill->refill_amount,"");
        if($response && @$response['status'] && @$response['status']=='COMPLETED' && !empty(@$response['id'])){
            return array(
                'status' => 200,
                'info' => $response
            );
        } else {
            return array(
                'status' => 400,
                'info' => $response
            );
        }
    }

    public static function VoidAuthorizePayment($authorize_payment_id){
        try{
            $token = self::PaypalAccessToken();
            if($token){
                $mode = config('paypal.mode');
                $clientId = config("paypal.{$mode}.client_id");
                $secret = config("paypal.{$mode}.client_secret");
                $base_url = config("paypal.{$mode}.base_url");
                $url = $base_url.'v2/payments/authorizations/'.$authorize_payment_id.'/void';
                // init curl
                $response = Curl::to($url)
                    ->withHeader('Accept: application/json')
                    ->withHeader('Accept-Language: en_US')
                    ->withHeader('Accept-Language: en_US')
                    ->withBearer($token)
                    ->returnResponseObject()
                    ->post();
                if($response && @$response->status==204){
                    return [
                        'status'=> 200
                    ];
                } else {
                    $result = json_decode($response->content,true);
                    return [
                        'status'=> 400,
                        'info' => $result,
                        'failed_reason' => @$result['details'][0]['description']
                    ];
                }
            }
        } catch (\Throwable $th) {
            \Log::error('Error while void authorize transaction',['Errors'=>$th]);
            return [
                'status'=> 400
            ];
        }
    }

    public static function PaypalAccessToken(){
        try {
            $mode = config('paypal.mode');
            $clientId = config("paypal.{$mode}.client_id");
            $secret = config("paypal.{$mode}.client_secret");
            $base_url = config("paypal.{$mode}.base_url");
            $url = $base_url.'v1/oauth2/token';
            // init curl
            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30, // Set a reasonable timeout
                CURLOPT_SSL_VERIFYPEER => false, // Disable SSL verification (not recommended for production)
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => 'grant_type=client_credentials&ignoreCache=true&return_authn_schemes=true&return_client_metadata=true&return_unconsented_scopes=true',
                CURLOPT_HTTPHEADER => array(
                    'Accept: application/json',
                    'Accept-Language: en_US',
                    'Content-Type: application/x-www-form-urlencoded'
                )
            ));
            curl_setopt($curl, CURLOPT_USERPWD, "$clientId:$secret");
            $response = curl_exec($curl);
            curl_close($curl);
            $tokenResponse = json_decode($response, true);
            if (isset($tokenResponse['access_token'])) {
                return $accessToken = $tokenResponse['access_token'];
            } else {
                return "";
            }
        } catch (\Throwable $th) {
            \Log::error("Paypal authentication failed",['Errors'=>$th]);
            return "";
        }
    }

    public static function deductPayment($order_no="",$paypal_card_id,$user_id,$amount,$refill_no){
        $payment_method = UserPaypalCardDetail::where('id',$paypal_card_id)->where('user_id',$user_id)->first();
        if($payment_method){
            $payment['intent'] = 'CAPTURE';
            $payment['purchase_units'][]['amount'] = array(
                "currency_code" => "USD",
                "value" => $amount
            );
            $payment['payment_source'] = array(
                "card" => array(
                    "vault_id" => $payment_method['paypal_vault_id']
                )
            );
            $provider = new PayPalClient;
            $provider->setApiCredentials(config('paypal'));
            $provider->getAccessToken();
            $request_id = 'capture-refill-payment'.time().rand(10000,999999);
            $response = $provider->setRequestHeader('PayPal-Request-Id', $request_id)
                                 ->createOrder($payment);
            if($response && @$response['status'] && @$response['status']=='COMPLETED' && !empty(@$response['id'])){
                return array(
                    'status' => 200,
                    'paymentInfo' => $response
                );
            } else {
                // \Log::error('Payment Failed',['OrderNo' => $order_no,'Amount' => '$'.$amount,'Refill No'=>$refill_no ?? '-','Failed Reason' => $response]);
                return array(
                    'status' => 400,
                    'paymentInfo' => $response,
                    'cardDetail' => $payment_method
                );
            }
        } else {
            \Log::error('Card details not found',['OrderNo'=>$order_no]);
            return array(
                'status' => 404,
                'message' => "We're are unable to find payment details."
            );
        }
    }

    public static function deletePaymentMethod($paypal_vault_token_id){
        $payment_method = UserPaypalCardDetail::where('paypal_vault_id',$paypal_vault_token_id)->first();
        if($payment_method){
            $provider = new PayPalClient;
            $provider->setApiCredentials(config('paypal'));
            $provider->getAccessToken();
            $request_id = 'delete-payment-card-details'.time().rand(10000,999999);
            $provider->setRequestHeader('PayPal-Request-Id', $request_id)
                                 ->deletePaymentSourceToken($paypal_vault_token_id);
            return array(
                'status' => 200
            );
        } else {
            \Log::error('Card details not found');
            return array(
                'status' => 404,
                'message' => "We're are unable to find payment details."
            );
        }
    }

    public static function refundAmount($captureId,$amount,$note)
    {
        try {
            $bearer_token = self::generatePaypalAccessToken();
            if(!empty($bearer_token)){
                // refund script
                $paypal_mode = config('paypal.mode');
                $paypal_config = config('paypal.'.$paypal_mode);
                $url = $paypal_config['base_url'].'v2/payments/captures/'.$captureId.'/refund';
                $post_data['amount'] = array(
                    "value" => $amount,
                    "currency_code" => 'USD',
                );
                $post_data['note_to_payer'] = "cancel order";
                $refund = Curl::to($url)
                                ->withData($post_data)
                                ->withBearer($bearer_token)
                                ->asJson(true)
                                ->post();
                if(@$refund['id']){
                    return ['status'=>200,'refund_id'=>$refund['id']];
                } else {
                    return ['status'=>400,'message'=>@$refund['details'][0]['description']];
                }
            } else {
                return ['status'=>400,'message'=>"Some technical issue. Please try again after some time."];
            }
        } catch (\Throwable $th) {
            \Log::error('Error while refund',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return ['status'=>400,'message'=>trans('admin/errors.server_error')];
        }
    }

    public static function generatePaypalAccessToken(){
        try {
            $paypal_mode = config('paypal.mode');
            $paypal_config = config('paypal.'.$paypal_mode);
            $url = $paypal_config['base_url'].'v1/oauth2/token';

            $curl = curl_init();
            curl_setopt_array($curl, array(
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => 'grant_type=client_credentials',
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Basic ' . base64_encode($paypal_config['client_id'] . ':' . $paypal_config['client_secret'])
                ),
                ));

                $response = curl_exec($curl);
                $response = json_decode($response,true);
                if($response && @$response['access_token']){
                    return $response['access_token'];
                } else{
                    \Log::error(['Error while generating paypal access token']);
                    return null;
                }
        } catch (\Throwable $th) {
            \Log::error('Error while refund',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return null;
        }
    }

    public static function deductPaymentUsingToken($token,$amount){
        $payment['intent'] = 'CAPTURE';
        $payment['purchase_units'][]['amount'] = array(
            "currency_code" => "USD",
            "value" => $amount
        );
        $payment['payment_source'] = array(
            "card" => array(
                "vault_id" => $token
            )
        );
        $provider = new PayPalClient;
        $provider->setApiCredentials(config('paypal'));
        $provider->getAccessToken();
        $request_id = 'capture-otc-order'.time().rand(10000,999999);
        $response = $provider->setRequestHeader('PayPal-Request-Id', $request_id)
                                ->createOrder($payment);
        if($response && @$response['status'] && @$response['status']=='COMPLETED' && !empty(@$response['id'])){
            return array(
                'status' => 200,
                'paymentInfo' => $response
            );
        } else {
            return array(
                'status' => 400,
                'paymentInfo' => $response
            );
        }
    }
}
