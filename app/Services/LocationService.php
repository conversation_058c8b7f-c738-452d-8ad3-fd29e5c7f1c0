<?php
namespace App\Services;

use Ixudra\Curl\Facades\Curl;

class LocationService {
    public static function get_client_ip()
    {
        // if (app()->environment('local')) {
        //     return '**************';
        // }
        if (getenv('HTTP_CLIENT_IP'))
            $ipAddress = getenv('HTTP_CLIENT_IP');
        else if (getenv('HTTP_X_FORWARDED_FOR'))
            $ipAddress = getenv('HTTP_X_FORWARDED_FOR');
        else if (getenv('HTTP_X_FORWARDED'))
            $ipAddress = getenv('HTTP_X_FORWARDED');
        else if (getenv('HTTP_FORWARDED_FOR'))
            $ipAddress = getenv('HTTP_FORWARDED_FOR');
        else if (getenv('HTTP_FORWARDED'))
            $ipAddress = getenv('HTTP_FORWARDED');
        else if (getenv('REMOTE_ADDR'))
            $ipAddress = getenv('REMOTE_ADDR');
        else
            $ipAddress = file_get_contents("http://ipecho.net/plain");

        if (app()->environment('production')) {
            $ipAddress = explode(',', $ipAddress);
            return $ipAddress[0];
        } else {
            return $ipAddress;
        }
    }

    public static function get_client_location($ip = null)
    {
        if (empty($ip)) {
            $ip = self::get_client_ip();
        }
        $url = 'http://ip-api.com/json/' . $ip;
        $result = json_decode(file_get_contents($url), true);
        return [
            'city' => $result['city'] ?? null,
            'state' => $result['region'] ?? null,
            'country' => $result['countryCode'] ?? null,
        ];
    }

    public static function get_ip_address_details($ip=null){
        try {
            if(empty($ip)){
                $ip = self::get_client_ip();
            }
            // ipapi.co
            $fetch_ip_details = self::ipAPICo($ip);
            if(!empty($fetch_ip_details)){
                return $fetch_ip_details;
            }
            // ip-api.com
            $fetch_ip_details = self::ipAPI($ip);
            if(!empty($fetch_ip_details)){
                return $fetch_ip_details;
            }
            // ip-info.io
            $fetch_ip_details = self::ipInfo($ip);
            if(!empty($fetch_ip_details)){
                return $fetch_ip_details;
            }
            // ipapi.io
            $fetch_ip_details = self::ipTimeapi($ip);
            if(!empty($fetch_ip_details)){
                return $fetch_ip_details;
            }
            return [
                'timezone' => null,
                'ip_address' => $ip,
                'location' => null,
            ];
        } catch (\Throwable $th) {
            return null;
        }
    }

    protected static function ipAPICo($ip){
        try {
            $url = 'https://ipapi.co/'.$ip.'/json/';
            $response = Curl::to($url)
                                ->withTimeout(3)
                                ->asJson(true)
                                ->get();
            if($response && @$response['timezone'] && @$response['ip']){
                return [
                    'timezone' => $response['timezone'],
                    'ip_address' => $response['ip'] ?? null,
                    'location' => @$response['city'].', '.@$response['region'].', '.@$response['country_name'],
                ];
            }
            return false;
        } catch (\Throwable $th) {
            \Log::error('Unable to determine the timezone from ipapi.co provider.',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return false;
        }
    }

    protected static function ipAPI($ip){
        try {
            $url = 'http://ip-api.com/json/'.$ip;
            $response = Curl::to($url)
                                ->withTimeout(2)
                                ->asJson(true)
                                ->get();
            if($response && @$response['status'] && $response['status']=='success'){
                return [
                    'timezone' => $response['timezone'],
                    'ip_address' => $response['query'] ?? null,
                    'location' => @$response['city'].', '.@$response['regionName'].', '.@$response['country'],
                ];
            }
            return false;
        } catch (\Throwable $th) {
            \Log::error('Unable to determine the timezone from ip-api provider.',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return false;
        }
    }

    protected static function ipInfo($ip){
        try {
            $url = 'https://ipinfo.io/'.$ip.'/json';
            $response = Curl::to($url)
                                ->withTimeout(2)
                                ->asJson(true)
                                ->get();
            if($response && @$response['timezone']){
                return [
                    'timezone' => $response['timezone'],
                    'ip_address' => $response['ip'] ?? null,
                    'location' => @$response['city'].', '.@$response['region'].', '.@$response['country'],
                ];
            }
            return false;
        } catch (\Throwable $th) {
            \Log::error('Unable to determine the timezone from ipinfo api provider.',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return false;
        }
    }

    protected static function ipTimeapi($ip){
        try {
            $url = 'https://timeapi.io/api/TimeZone/ip?ipAddress='.$ip.'/json';
            $response = Curl::to($url)
                                ->withTimeout(2)
                                ->asJson(true)
                                ->get();
            if($response && @$response['timeZone']){
                return self::ipAPI($ip);
            }
            return false;
        } catch (\Throwable $th) {
            \Log::error('Unable to determine the timezone from timeapi.io api provider.',['Error'=>$th->getMessage(),'Errors'=>$th]);
            return false;
        }
    }
}
