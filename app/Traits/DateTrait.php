<?php

namespace App\Traits;

use Carbon\Carbon;

trait DateTrait
{
    public static function GetUserTimezone($request) {
        if($request && $request->user() && $request->user()->user_timezone){
            return $request->user()->user_timezone;
        } else {
            return config('app.timezone');
        }
    }

    public static function GetDate($date_time,$timezone) {
        $date_time = str_replace(' ', '', $date_time);
        return ($date_time) ? Carbon::parse($date_time)->timezone($timezone)->format('M d, Y') : null;
    }

    public static function GetTime($date_time,$timezone) {
        $date_time = str_replace(' ', '', $date_time);
        return ($date_time) ? Carbon::parse($date_time)->timezone($timezone)->format('h:i A T') : null;
    }

    public static function GetDateTime($date_time,$request) {
        $date_time = str_replace(' ', '', $date_time);
        return ($date_time) ? Carbon::parse($date_time)->timezone(self::GetUserTimezone($request))->format('M d, Y h:i A T') : null;
    }

    public static function GetHumanDate($date_time,$request) {
        $date_time = str_replace(' ', '', $date_time);
        return ($date_time) ? Carbon::parse($date_time)->timezone(self::GetUserTimezone($request))->diffForHumans() : null;
    }

    // public static function GetDefaultDate($date) {
    //     return Carbon::parse($date)->timezone(self::GetUserTimezone())->format('M d, Y T');
    // }

    public static function DateTimeConvertToUTC($date, $is_from_date, $request, $request_from_date = null, $from_date = null){

        $filter_date = Carbon::parse(Carbon::parse($date))->timezone(self::GetUserTimezone($request))->format('Y-m-d H:i:00');
        if($is_from_date){
            $to = Carbon::createFromFormat('m/d/Y H:i:s', $date.' 00:00:00')->format('Y-m-d H:i:00');
            $to_date = Carbon::parse($to);
            if($filter_date>$to_date){
                $differenceInMinutes = $to_date->diffInMinutes($filter_date);
                return Carbon::parse($to_date)->subMinutes($differenceInMinutes)->format('Y-m-d H:i:00');
            } else {
                $differenceInMinutes = $to_date->diffInMinutes($filter_date);
                return Carbon::parse($to_date)->addMinutes($differenceInMinutes)->format('Y-m-d H:i:00');
            }
            return $filter_date;
        } else {
            $date1 = Carbon::parse($request_from_date);
            $date2 = Carbon::parse($date);
            $differenceInDays = $date1->diffInDays($date2) + 1;
            $to = Carbon::createFromFormat('m/d/Y H:i:s', $date.' 23:59:59')->format('Y-m-d H:i:00');
            return $to_date = Carbon::parse($from_date)->addDays($differenceInDays)->subMinutes(1)->format('Y-m-d H:i:59');
        }
    }
}
