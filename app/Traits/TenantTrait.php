<?php

namespace App\Traits;

use App\Models\Tenant;
use Illuminate\Support\Facades\DB;

trait TenantTrait
{
    public static function TenantDomainUrl($domain) {
        $protocol = env('APP_ENV') === 'local' ? 'http://' : 'https://';
        $url = $protocol;
        if ($domain) {
            $url .= ltrim($domain, '/');
        }
        return $url;
    }

    public static function CheckPharmacySubdomain($sub_domain,$id=null){
        try {
            $superAdminSubDomain = self::getSubdomain();
            if ($superAdminSubDomain!=null && $superAdminSubDomain == $sub_domain) {
                return [
                    'status'  => 400,
                    'message' => "Already taken"
                ];
            }
            $pharmacy_name = strtolower(str_replace(' ','-',$sub_domain));
            $tenant = Tenant::select('id')
                    ->where('id', $pharmacy_name)
                    ->whereNot('id', $id)
                    ->first();
            if ($tenant) {
                return [
                    'status' => 400,
                    'message' => "Already taken"
                ];
            }
            $centralDomain = config('tenancy.central_domains')[0];
            $tenant_sub_domain = $pharmacy_name . '.' . $centralDomain;
            $domain = DB::table('domains')
                        ->where('domain', $tenant_sub_domain)
                        ->where('tenant_id','!=', $id)
                        ->count();
            if ($domain > 0) {
                return [
                    'status' => 400,
                    'message' => "Already taken"
                ];
            }
            return [
                'status' => 200,
                "message" => "Available"
            ];
        } catch (\Throwable $th) {
            return [
                'status' => 400,
                "message" => "Something went wrong. Please try again"
            ];
        }
    }

    protected static function getSubdomain() {
        try {
            $url = config('tenancy.super_admin_central_domain');
            if (!preg_match("~^(?:f|ht)tps?://~i", $url)) {
                $url = "http://" . $url;
            }

            $host = parse_url($url, PHP_URL_HOST);

            $parts = explode('.', $host);

            if ($parts[0] === 'www') {
                array_shift($parts);
            }

            if (end($parts) === 'localhost') {
                return (count($parts) === 2) ? $parts[0] : null;
            }


            if (count($parts) > 2) {
                return $parts[0];
            }

            return null;
        } catch (\Throwable $th) {
            return null;
        }
    }
}
