<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

Route::controller(App\Http\Controllers\Tenant\API\User\AppSettingController::class)->group(function () {
    Route::get('app-settings' , 'AppSettings');
});


Route::controller(App\Http\Controllers\Tenant\API\User\ProductController::class)->group(function () {
    Route::get('fetch-landing-page-data' , 'FetchLandingPageData');
    Route::get('fetch-all-categories' , 'GetAllCategories');
    Route::get('get-header-categories' , 'GetHeaderCategories');
    Route::post('get-products' , 'GetProducts');
    Route::get('get-product-details/{slug}' , 'ProductDetails');
    Route::post('user-product-lead' , 'CollectUserProductLead');
});
