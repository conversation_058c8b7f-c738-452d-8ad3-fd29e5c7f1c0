<?php

use Illuminate\Support\Facades\Route;


// foreach ( as $domain) {
    Route::domain(config('tenancy.super_admin_central_domain'))->group(function () {
        if(env('APP_ENV')=='local'){
            Route::get('/plan-image/{fold1?}/{fold2?}/{file?}', [App\Http\Controllers\ImageController::class, 'GetPlan']);

            Route::get('/products/{pharmacy_asset}/{tenant_id}/{products}/{product_name}/{file?}', [App\Http\Controllers\ImageController::class, 'GetProductImage']);
        }

        Route::get('/{any}', function () {
            return view('index');
        })->where('any', '.*');
    });
// }



