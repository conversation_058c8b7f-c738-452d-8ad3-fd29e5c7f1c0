<?php

declare(strict_types=1);

use App\Http\Middleware\Tenant\IsTenantEnabled;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\InitializeTenancyByDomain;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
|--------------------------------------------------------------------------
| Tenant Routes
|--------------------------------------------------------------------------
|
| Here you can register the tenant routes for your application.
| These routes are loaded by the TenantRouteServiceProvider.
|
| Feel free to customize them however you want. Good luck!
|
*/

Route::middleware([
    'web',
    InitializeTenancyByDomain::class,
    PreventAccessFromCentralDomains::class,
])->group(function () {
    if(env('APP_ENV')=='local'){
        Route::get('/categories/{env?}/{fold1?}/{fold2?}/{fold3?}/{file?}', [App\Http\Controllers\ImageController::class, 'GetFiles']);
        Route::get('/products/{env?}/{fold1?}/{fold2?}/{fold3?}/{file?}', [App\Http\Controllers\ImageController::class, 'GetFiles']);
        Route::get('/images/{env?}/{fold1?}/{fold2?}/{file?}', [App\Http\Controllers\ImageController::class, 'GetImage']);
        Route::get('/frontend-images/{env?}/{fold1}/{fold2}/{fold3}/{file?}', [App\Http\Controllers\ImageController::class, 'GetFrontendImage']);
    }
    Route::middleware([IsTenantEnabled::class])->group(function () {
        // api
        Route::prefix('api')->group(function () {
            require base_path('routes/tenant/api.php');
        });
        // admin
        Route::prefix('api/admin')->group(function () {
            require base_path('routes/tenant/admin.php');
        });
        Route::get('/{tenant_any}', function () {
            return view('tenant.index');
        })->where('tenant_any', '.*');
    });

});
