<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    'company' => [
        'name' => env('COMPANY_NAME'),
        'address' => env('COMPANY_ADDRESS'),
        'support_email' => env('COMPANY_SUPPORT_EMAIL'),
        'support_phone_number' => env('COMPANY_SUPPORT_PHONE_NUMBER',''),
        'email_logo_url' => env('SITE_EMAIL_LOGO'),
    ],

    'mailgun' => [
        'api_url' => env('MAILGUN_API_URL'),
        'api_key' => env('MAILGUN_API_KEY'),
        'domain_name' => env('MAILGUN_DOMAIN_NAME'),
        'reply_to' => env('MAILGUN_REPLY_TO'),
        'from_email_address' => env('MAILGUN_FROM_EMAIL_ADDRESS'),
    ],

    'google_recaptcha' => [
        'secret_key' => env('GOOGLE_RECAPTCHA_SECRET_KEY'),
    ],

];
