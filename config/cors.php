<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Cross-Origin Resource Sharing (CORS) Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your settings for cross-origin resource sharing
    | or "CORS". This determines what cross-origin operations may execute
    | in web browsers. You are free to adjust these settings as needed.
    |
    | To learn more: https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS
    |
    */

    // 'paths' => ['api/*', 'sanctum/csrf-cookie'],

    'paths' => [
        '*',
        'api/*',
    ],

    'allowed_methods' => ['*'],

    // 'allowed_origins' => [
    //     'localhost:8004',
    //     '*.localhost:8004',
    //     env('APP_URL', ''),
    //     env('TENANCY_CENTRAL_DOMAINS', ''),
    // ],

    // 'allowed_origins_patterns' => [

    // ],

    'allowed_origins' => [
        env('APP_URL'),
        env('CHECKOUT_PORTAL_DOMAIN'),
    ],

    'allowed_origins_patterns' => [
        // Allow any subdomain of localhost:8004
        '/^http:\/\/([a-z0-9-]+\.)?localhost:8004$/',

        // Allow any subdomain of TENANCY_CENTRAL_DOMAINS
        '/^https?:\/\/([a-z0-9-]+\.)?' . preg_quote(env('TENANCY_CENTRAL_DOMAINS', 'localhost'), '/') . '$/',
    ],

    'allowed_headers' => ['*'],

    'exposed_headers' => [],

    'max_age' => 0,

    'supports_credentials' => true,

];
