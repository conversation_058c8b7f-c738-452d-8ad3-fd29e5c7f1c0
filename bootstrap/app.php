<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use League\OAuth2\Server\Exception\OAuthServerException;
use Illuminate\Support\Facades\Route;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Illuminate\Auth\Access\AuthorizationException;
use Symfony\Component\Routing\Exception\RouteNotFoundException;
use Illuminate\Http\Exceptions\PostTooLargeException;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\TooManyRequestsHttpException;
use Stancl\Tenancy\Exceptions\TenantCouldNotBeIdentifiedOnDomainException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->validateCsrfTokens(except: [
            'admin/*',
            'api/admin/*',
            'api/*',
        ]);
        $middleware->redirectGuestsTo('');

        $middleware->alias([
            'sanctum-cookie' => \App\Http\Middleware\SanctumCookieAuth::class,
            'auth-sanctum' => \App\Http\Middleware\CentralUserSanctumAuth::class,
            'tenant-sanctum-cookie' => \App\Http\Middleware\Tenant\TenantSanctumCookieAuth::class,
            'tenant-auth-sanctum' => \App\Http\Middleware\Tenant\TenantUserSanctumAuth::class,
            'tenant-guest' => \App\Http\Middleware\Tenant\GuestMiddleware::class,
            'verify-2fa' => \App\Http\Middleware\Verify2FAAuthenticationMiddleware::class
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        $exceptions->dontReport([
            OAuthServerException::class,
            TenantCouldNotBeIdentifiedOnDomainException::class
        ]);

        $exceptions->render(function (TenantCouldNotBeIdentifiedOnDomainException $e, Request $request) {
            return abort(404);
        });

        // NotFoundHttpException
        $exceptions->render(function (NotFoundHttpException $e, Request $request) {
            if ($request->is('api/*') || $request->is('admin/*')) {
                return response()->json(['message' => 'End point not found.'], 404);
            }
        });

        // RouteNotFoundException
        $exceptions->render(function (RouteNotFoundException $e, Request $request) {
            if ($request->is('api/*') || $request->is('admin/*')) {
                return response()->json(['message' => 'End point not found.'], 404);
            }
        });

        // MethodNotAllowedHttpException
        $exceptions->render(function (MethodNotAllowedHttpException $e, Request $request) {
            if ($request->is('api/*') || $request->is('admin/*')) {
                return response()->json(['message' => 'End point not allowed.'], 404);
            }
        });

        // AuthorizationException
        $exceptions->render(function (AuthorizationException $e, Request $request) {
            if ($request->is('api/*') || $request->is('admin/*')) {
                return response()->json(['message' => 'Access denied.'], 403);
            }
        });

        // PostTooLargeException
        $exceptions->render(function (PostTooLargeException $e, Request $request) {
            if ($request->is('api/*') || $request->is('admin/*')) {
                return response()->json([
                    'status' => 200,
                    'message' => 'The file size exceeds the allowed limit of 3 MB.'
                ]);
            }
        });

        // TooManyRequestsHttpException
        $exceptions->render(function (TooManyRequestsHttpException $e, Request $request) {
            if ($request->is('api/*') || $request->is('admin/*')) {
                return response()->json(['message' => 'Too many attempts.'], 429);
            }
        });
    })->create();
